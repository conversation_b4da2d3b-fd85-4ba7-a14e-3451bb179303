Name,Instance ID(AWS),Movegroup,OS,Internal IP,External IP,Appname,Environment,Layer,App Group,Owner,CPU Cores,Memory GB,Disk GB,Disk IOPS,Disk Count,Total Disk Size,Listener Port(s),HA,Shared Storage(SAN) Dependency,File Share Dependency,DNS Records,DownStream App1,DownStream App2,Hosts File Used?,Migration Path,Target Size,Target Disk Type,UseZone,Project,Region,Zone,VPC,Subnet Id,Security Group Id,Target Internal IP,IAM-Profile,Need for External IP,Tags,PrivateIPNum,SubnetIPStartNum,SubnetIPEndNum,IP OK?
hypatia-test,,mg1,Debian 12,*********,**************,App2,Prod,Web,Marketing,User2,2,4,,,0,0,,No,No,,,,,,Rehost,t2.xlarge,gp2,a,subnet-01,sg-1a2b3c4d,sg-1a2b3c4d-a,prdvpc-01,subnet-01,sg-1a2b3c4d,************,<EMAIL>,Yes,appname:app2|env:prod|applayer:web|appgroup:marketing|appowner:user2|movegroup:mg1,168453132,168453122,168453373,OK
