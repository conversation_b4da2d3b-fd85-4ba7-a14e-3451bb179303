import openpyxl
import os
import logging
import pandas as pd


# --- Configuration Constants for Azure Bill Processing ---
TARGET_SHEET_AZURE = "Consumption Mapping"

DESTINATION_SERVICE_COLUMNS = ['Azure Services', 'AZURE Service']
DESTINATION_CONSUMPTION_COLUMN = 'Consumption'

START_ROW_AZURE = 2


def process_azure_bill_gcp(request, rootpath):
    """
    Processes an uploaded Azure bill CSV file, extracts 'metercategory' and 'cost' data,
    aggregates costs for duplicate categories, and populates into a standardized
    GCP migration scoping template.

    Args:
        request: The request object containing the user's uploaded CSV file.
        rootpath (str): The root directory path of the application.

    Returns:
        tuple: (output_path, error_message, success_message, status_code).
    """
    log_file_path = os.path.join(rootpath, 'app.log')
    logging.basicConfig(
        filename=log_file_path,
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        force=True
    )

    try:
        logging.debug("Starting Azure bill CSV processing.")

        # --- 1. Define File Paths ---
        source_file = request.files["azure-bill-csv"]
        destination_template_path = f"{rootpath}/static/assets/excels/Azure-to-GCP-migration-scoping-with Azure-Bills .xlsx"
        output_path = f"{rootpath}/Azure-to-GCP-migration-scoping-with-Azure-Bill-output.xlsx"
        logging.debug(f"Source file name: {source_file.filename}")
        logging.debug(f"Template path: {destination_template_path}")
        logging.debug(f"Output path: {output_path}")

        # --- 2. Read and Aggregate Data from CSV ---
        logging.info(f"Reading data from uploaded CSV: '{source_file.filename}'")

        df = pd.read_csv(source_file)

        # Normalize column names: strip spaces + lowercase
        df.columns = [c.strip().lower() for c in df.columns]
        logging.debug(f"Normalized CSV columns: {df.columns.tolist()}")

        if "metercategory" not in df.columns or "cost" not in df.columns:
            error_msg = f"CSV must contain 'metercategory' and 'cost' columns. Found: {df.columns.tolist()}"
            logging.error(error_msg)
            return "", error_msg, error_msg, 1

        # Group by metercategory and sum cost
        grouped = df.groupby("metercategory", as_index=False)["cost"].sum()
        grouped = grouped.sort_values(by="cost", ascending=False)
        data_to_paste = grouped.to_dict(orient="records")
        logging.info(f"Aggregated {len(data_to_paste)} unique Meter Categories.")

        # --- 3. Load the Template Workbook ---
        if not os.path.exists(destination_template_path):
            error_msg = f"Destination template file not found at '{destination_template_path}'."
            logging.error(error_msg)
            raise FileNotFoundError(error_msg)

        dest_workbook = openpyxl.load_workbook(destination_template_path)

        if TARGET_SHEET_AZURE not in dest_workbook.sheetnames:
            error_msg = f"Target sheet '{TARGET_SHEET_AZURE}' not found in the template workbook."
            logging.error(error_msg)
            raise ValueError(error_msg)

        dest_sheet = dest_workbook[TARGET_SHEET_AZURE]
        dest_header = [cell.value for cell in dest_sheet[1]]

        dest_service_col_idx = -1
        for col_name in DESTINATION_SERVICE_COLUMNS:
            if col_name in dest_header:
                dest_service_col_idx = dest_header.index(col_name) + 1
                break

        if dest_service_col_idx == -1:
            error_msg = f"Could not find any of the service columns {DESTINATION_SERVICE_COLUMNS} in the template."
            logging.error(error_msg)
            raise ValueError(error_msg)

        dest_consumption_col_idx = dest_header.index(DESTINATION_CONSUMPTION_COLUMN) + 1
        logging.debug(f"Destination service column index: {dest_service_col_idx}")
        logging.debug(f"Destination consumption column index: {dest_consumption_col_idx}")

        # --- 4. Write Data to Template ---
        logging.info(f"Clearing old data and writing {len(data_to_paste)} rows.")
        for row in range(START_ROW_AZURE, dest_sheet.max_row + 200):
            dest_sheet.cell(row=row, column=dest_service_col_idx).value = None
            dest_sheet.cell(row=row, column=dest_consumption_col_idx).value = None

        for index, data_row in enumerate(data_to_paste, start=START_ROW_AZURE):
            dest_sheet.cell(row=index, column=dest_service_col_idx).value = data_row['metercategory']
            dest_sheet.cell(row=index, column=dest_consumption_col_idx).value = data_row['cost']
            logging.debug(f"Wrote row {index}: {data_row}")

        # --- 5. Save Output ---
        dest_workbook.save(output_path)
        success_msg = f"Successfully updated '{TARGET_SHEET_AZURE}' with CSV data and saved to '{output_path}'."
        logging.info(success_msg)

        return output_path, "", success_msg, 0

    except Exception as e:
        error_msg = f"An unexpected error occurred: {e}"
        logging.error(error_msg, exc_info=True)
        return "", error_msg, error_msg, 1
