import pandas as pd
import os

def gta_m2vm1(request,rootpath):
    moveg = request.form['move-group']
    file1 = request.files['csv-file']
    file1.save(os.path.join(rootpath,"migration", file1.filename))
    file2 = request.files['excel-file']
    file2.save(os.path.join(rootpath,"migration", file2.filename))

    file1_path = os.path.join(rootpath,"migration", file1.filename)
    file2_path = os.path.join(rootpath,"migration", file2.filename)
    out_path = os.path.join(rootpath,"migration", "ams-import.csv")


    export_df = pd.read_csv(file1_path)
    mapping_df = pd.read_csv(file2_path)
    error = ""

    try:
        columns_to_remove = ['mgn:launch:tag:instance:AWSApplicationMigrationServiceManaged', 'mgn:server:lifecycle-state', 'mgn:server:replication-state', 'mgn:server:replication-type']
        export_df.drop(columns_to_remove, axis=1, inplace=True)
        mapping_df = mapping_df[mapping_df["Movegroup"] == moveg]

        for i in range(len(mapping_df)):
            for j in range(len(export_df)):
                if mapping_df["Name"][i] == export_df["mgn:server:tag:Name"][j]:
                    export_df.at[j,"mgn:app:name"] = moveg
                    export_df.at[j,"mgn:launch:nic:0:subnet-id"] = mapping_df["Subnet Id"][i]
                    export_df.at[j,"mgn:launch:nic:0:security-group-id:0"] = mapping_df["Security Group Id"][i]
                    if mapping_df["Target Internal IP"][i] != float('nan'):
                        export_df.at[j,"mgn:launch:nic:0:private-ip:0"] = mapping_df["Target Internal IP"][i]
                    if mapping_df["Target Size"][i] != export_df["mgn:launch:instance-type"][j]:
                        export_df.at[j,"mgn:launch:instance-type"] = mapping_df["Target Size"][i]
                    if mapping_df["IAM-Profile"][i] != float('nan'):
                        export_df.at[j,"mgn:launch:iam-instance-profile:name"] = mapping_df["IAM-Profile"][i]
                    split_df = mapping_df['Labels'][i].split('\n')
                    for tags in split_df:
                        tag = tags.split(':')
                        export_df.at[j,"mgn:launch:tag:instance:"+tag[0]] = tag[1]

        export_df.to_csv(out_path,index=False)
        error = ""
    except:
        error = "Please Enter correct files or select valid movegroup."

    if os.path.isfile(out_path) and error == "":
        with open(out_path,'r') as f:
            x = len(f.readlines())
        if x == 1 or x == 0:
            msg = "No Vms found for provided movegroup or check the treatment is set to Rehost."
        else:
            msg = "csv file created and downloaded successfully."
        return out_path,"",msg,0
    else:
        err1 = "Please check whether you have uploaded the proper files."
        log_path = os.path.join(rootpath,'templates','log.html')
        with open(log_path, 'w') as f:
            f.write('<!DOCTYPE html>\n<html>\n<head>\n<style>\n.body {\nfont-family: Arial,\nsans-serif;}\n.text {\nfont-size: 16px;\nline-height: 1.5;\npadding: 20px;\nbackground-color: #f5f5f5;\nborder: 1px solid #ccc;}\n.scroll{\noverflow-y: scroll;}\n</style>\n<title>TFVARS Generation detailed log</title>\n</head>\n<body>\n<div class="body text scroll">\n<pre>'+error+'</pre>\n</div>\n</body>\n</html>')
        f.close()
        return "",error,err1,2
    

            
            