boto3==1.38.11
botocore==1.38.11
Flask==2.2.2
Flask_Cors==3.0.10
openpyxl==3.1.2
pandas
numpy
pdfkit==1.0.0
protobuf==4.23.2
Werkzeug==2.2.2
XlsxWriter==3.0.9
google-cloud-compute==1.10.1
regex
python-jose[cryptography]==3.3.0
requests==2.32.3
google-auth
google-auth-oauthlib
google-auth-httplib2
google-cloud-iam
keyring
ansi2html
google-cloud-resource-manager
pyyaml
pdfplumber
awscli==1.40.10
oci
oci-cli
google-cloud-bigquery
google-api-python-client
pandas
google-genai
pypubsub
python-dateutil==2.8.2
urllib3==1.26.14
pyodbc==4.0.35
psycopg2-binary==2.9.5
mysql-connector-python

 # VMs, Disks, Networks, Subnets, Routes, Firewalls, VPNs, LB Components, Images, Templates, Health Checks, SSL Certs, Regions
google-cloud-storage >= 2.7.0 # Cloud Storage Buckets
google-cloud-pubsub >= 2.13.0 # Pub/Sub Topics & Subscriptions
google-cloud-container >= 2.19.0 # GKE Clusters
google-cloud-functions >= 1.10.0 # Cloud Functions (V1 & V2)
google-cloud-appengine-admin >= 1.0.0 # App Engine Services & Firewall
google-cloud-dns >= 0.34.0 # DNS Managed Zones & Policies
google-cloud-kms >= 2.16.0 # KMS Keys
google-cloud-secret-manager >= 2.16.0 # Secret Manager Secrets
google-cloud-data-fusion >= 1.5.0 # Data Fusion Instances
google-cloud-redis >= 2.12.0 # Memorystore for Redis
google-cloud-memcache >= 1.6.0 # Memorystore for Memcached
google-cloud-scheduler >= 2.8.0 # Cloud Scheduler Jobs
google-cloud-run >= 0.10.0 # Cloud Run Services
google-cloud-bigtable >= 2.17.0 # Bigtable client
google-cloud-spanner >= 2.17.0 # Cloud Spanner Instances
google-cloud-orchestration-airflow >= 1.11.1 # Cloud Composer Environments
google-cloud-dataproc >= 5.5.0 # Dataproc Clusters
google-cloud-artifact-registry >= 1.10.0 # Artifact Registry Repositories (incl. GCR via AR)
google-cloud-dataflow-client >= 0.8.8 # Dataflow Jobs (Updated to avoid apache-beam build issue)
google-cloud-resource-manager >= 1.10.0 # Project IAM Policies
google-cloud-vpc-access >= 1.4.0 # Serverless VPC Access Connectors
google-cloud-monitoring >= 2.14.0 # Monitoring Alert Policies & Notification Channels
google-cloud-logging >= 3.5.0 # Logging Sinks
google-cloud-build >= 3.15.0 # Cloud Build Triggers
google-cloud-org-policy

tabulate >= 0.9.0 # If creating a console table formatter

# Production Dependencies for Enhanced Features
# Performance and Monitoring
psutil >= 5.9.0 # System resource monitoring
prometheus-client >= 0.17.0 # Metrics collection

# Security and Compliance
cryptography >= 41.0.0 # Enhanced security features
pydantic >= 2.0.0 # Data validation

# Resilience and Error Handling
tenacity >= 8.2.0 # Advanced retry mechanisms
backoff >= 2.2.0 # Exponential backoff

# Logging and Observability
structlog >= 23.1.0 # Structured logging
colorama >= 0.4.6 # Colored console output

# Development and Testing (optional)
pytest >= 7.4.0 # Testing framework
pytest-cov >= 4.1.0 # Coverage reporting
pytest-mock >= 3.11.0 # Mocking for tests
black >= 23.7.0 # Code formatting
flake8 >= 6.0.0 # Linting
mypy >= 1.5.0 # Type checking
bandit >= 1.7.5 # Security linting

# Documentation (optional)
sphinx >= 7.1.0 # Documentation generation
sphinx-rtd-theme >= 1.3.0 # Documentation theme

# Container and Deployment (optional)
gunicorn >= 21.2.0 # WSGI server
uvicorn >= 0.23.0 # ASGI server
click >= 8.0.0 # Alternative CLI argument parser
google-cloud-recommender