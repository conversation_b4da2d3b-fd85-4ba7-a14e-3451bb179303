## Description

The Network Access Control List (NACL) function provide stateless filtering of ingress and egress network traffic to AWS resources. It is recommended that no NACL allows unrestricted ingress access to remote server administration ports, such as SSH to port `22` and RDP to port `3389`, using either the TCP (6), UDP (17) or ALL (-1) protocols.

Public access to remote server administration ports, such as 22 and 3389, increases resource attack surface and unnecessarily raises the risk of resource compromise.

## Remediation

### From Console:

Perform the following:

1. Log into the AWS Management Console at https://console.aws.amazon.com/vpc/home.
2. In the left pane, click `Network ACLs`.
3. For each network ACL to remediate, perform the following:
    - Select the network ACL.
    - Click the `Inbound Rules` tab.
    - Click `Edit inbound rules`.
    - Either A) update the Source field to a range other than 0.0.0.0/0, or, B) Click `Delete` to remove the offending inbound rule.
    - Click `Save`.
