## Description

When enabling the Metadata Service on AWS EC2 instances, users have the option of using either Instance Metadata Service Version 1 (IMDSv1; a request/response method) or Instance Metadata Service Version 2 (IMDSv2; a session-oriented method).

Allowing Version 1 of the service may open EC2 instances to Server-Side Request Forgery (SSRF) attacks, so Amazon recommends utilizing Version 2 for better instance security.

## Remediation

### From Console:

1. Sign in to the AWS Management Console and navigate to the EC2 dashboard at (https://console.aws.amazon.com/ec2/)[https://console.aws.amazon.com/ec2/].
2. In the left navigation panel, under the `INSTANCES` section, choose `Instances`.
3. Select the EC2 instance that you want to examine.
4. Choose `Actions > Instance Settings > Modify instance metadata options`.
5. Ensure `Instance metadata service` is set to `Enable` and set `IMDSv2` to `Required`.
6. Repeat steps no. 1 – 5 to perform the remediation process for other EC2 
Instances in the all applicable AWS region(s).
### From Command Line:

1. Run the `describe-instances` command using appropriate filtering to list the IDs of all the existing EC2 instances currently available in the selected region:

```bash
aws ec2 describe-instances --region <region-name> --output table --query "Reservations[*].Instances[*].InstanceId"
```

2. The command output should return a table with the requested instance IDs.
3. Now run the `modify-instance-metadata-options` command using an instance ID returned at the previous step to update the Instance Metadata Version:

```bash
aws ec2 modify-instance-metadata-options --instance-id <instance-id> --http-tokens required --region <region-name>
```

4. Repeat steps no. 1 – 3 to perform the remediation process for other EC2 Instances in the same AWS region.
5. Change the region by updating `--region` and repeat the entire process for other regions.