## Description

Ensure your *Alternate Contacts* for security are correct in the AWS account settings page of your AWS account.

In addition to the primary contact information, you must enter the security contacts:
- **Security**:  When you have notifications from the AWS Abuse team for potentially fraudulent activity on your AWS account. Any notification related to security.

As a best practice, avoid using contact information for individuals, and instead use group email addresses and shared company phone numbers.

AWS uses the security contact information to inform you of critical service events such as security issues. Keeping your security contact information up to date ensure timely delivery of critical information to the relevant stakeholders. Incorrect security contact information may result in communications delays that could impact your organization security.

## Remediation

There is no API available for setting security contact information - you must log in to the AWS console to verify and set your security contact information.

1. Sign into the AWS console, and navigate to the [Account Settings](https://console.aws.amazon.com/billing/home?#/account).
2. Verify that the information in the **Alternate Contacts**  *Security* section is correct and complete. If changes are required, click **Edit**, make your changes and then click **Update**.
