## Description

With the creation of an AWS account, a 'root user' is created that cannot be disabled or deleted. That user has unrestricted access to and control over all resources in the AWS account. It is highly recommended that the use of this account be avoided for everyday tasks.

The 'root user' has unrestricted access to and control over all account resources. Use of it is inconsistent with the principles of least privilege and separation of duties, and can lead to unnecessary harm due to error or account compromise.

## Remediation

If you find that the 'root' user account is being used for daily activity to include administrative tasks that do not require the 'root' user:
1. Change the 'root' user password.
2. Deactivate or delete any access keys associate with the 'root' user.
**Remember, anyone who has 'root' user credentials for your AWS account has unrestricted access to and control of all the resources in your account, including billing information.
