## Description

AWS CloudTrail is a web service that records AWS API calls for your account and delivers log files to you. The recorded information includes the identity of the API caller, the time of the API call, the source IP address of the API caller, the request parameters, and the response elements returned by the AWS service. CloudTrail provides a history of AWS API calls for an account, including API calls made via the Management Console, SDKs, command line tools, and higher-level AWS services (such as CloudFormation).

The AWS API call history produced by CloudTrail enables security analysis, resource change tracking, and compliance auditing. Additionally,

- ensuring that a multi-regions trail exists will ensure that unexpected activity occurring in otherwise unused regions is detected
- ensuring that a multi-regions trail exists will ensure that Global Service Logging is enabled for a trail by default to capture recording of events generated on AWS global services
- for a multi-regions trail, ensuring that management events configured for all type of Read/Writes ensures recording of management operations that are performed on all resources in an AWS account

## Remediation

Perform the following to enable global (Multi-region) CloudTrail logging:

### Via the management Console

1. Sign in to the AWS Management Console and open the IAM console at [cloudtrail](https://console.aws.amazon.com/cloudtrail)
2. Click on `Trails` on the left navigation pane
3. Click `Get Started Now` , if presented.

  - Click `Add new trail`.
  - Enter a trail name in the `Trail name` box.
  - Set the `Apply trail to all regions` option to `Yes`.
  - Specify an S3 bucket name in the `S3 bucket` box.
  - Click `Create`.

4. If 1 or more trails already exist, select the target trail to enable for global logging.
5. Click the edit icon (pencil) next to `Apply trail to all regions`, Click `Yes` and Click `Save`.
6. Click the edit icon (pencil) next to `Management Events` click `All` for setting `Read/Write Events` and Click `Save`.

### Via CLI

```bash
aws cloudtrail create-trail --name <trail_name> --bucket-name <s3_bucket_for_cloudtrail> --is-multi-region-trail aws cloudtrail update-trail --name <trail_name> --is-multi-region-trail
```

**Note**: Creating CloudTrail via CLI without providing any overriding options configures `Management Events` to set `All` type of `Read/Writes` by default.

**Default Value**: Not Enabled
