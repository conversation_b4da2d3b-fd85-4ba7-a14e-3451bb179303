## Description

The "root" account has unrestricted access to all resources in the AWS account. It is highly recommended that the use of this account be avoided.

The "root" account is the most privileged AWS account. Minimizing the use of this account and adopting the principle of least privilege for access management will reduce the risk of accidental changes and unintended disclosure of highly privileged credentials.

## Remediation

Follow the remediation instructions of the Ensure IAM policies are attached only to groups or roles recommendation.
