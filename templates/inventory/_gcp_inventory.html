<!--G<PERSON> invenotry-->
<div id="software" class="tab-pane fade in active show">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate GCP Inventory Workbook for an organisation
				<span class="text" style="color: #29c8e1">by giving org id</span>
			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'soft_req')">Requirements</button>
					<div id="soft_req" class="tabcontent">
						<p>
							1. Needs <span class="text" style="color: black; font-weight: bold"> Browser
							</span>and<span class="text" style="color: black; font-weight: bold"> Viewer
							</span> access at Organization Level for Cloud Run Service Account <span
								class="text"
								style="color: blue"><EMAIL></span><br>
							2. Will also work with <span class="text"
								style="color: black; font-weight: bold"> Viewer </span> access at Project
							level when projects have been selected.<br>
						</p>
						<div>
							<img src="./static/assets/images/permissions.png"
								style="margin-left: 10%; margin-top: -6%" width="90%" height="90%" />
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'soft_steps')">Steps</button>
					<div id="soft_steps" class="tabcontent">

						<p>1. Fill in at least one field based on your requirements.<br>
							2. Enter GCP Organization ID to create an inventory for the whole
							organization.<br>
							3. Enter Folder IDs (comma-separated) to create an inventory for specific
							folders.<br>
							4. Enter Project IDs (comma-separated) to create an inventory for specific
							projects.<br>
							5. To include Redis, provide comma separated regions. Default region is
							us-central1.
					</div>
					<button class="tablinks" onclick="openCity(event, 'soft_out')">Output</button>
					<div id="soft_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1_D9pkJiBefJ1MXF9KGN97sz_h1qmYFT-/view?usp=sharing&ouid=105634715941788046732&rtpof=true&sd=true"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/gcpinvent1.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'soft_vid')">Video</button>
					<div id="soft_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/1NZ96Cgwku385dW0A8MSAssljRudJH3n3/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'soft_wn')">What Next?</button>
					<div id="soft_wn" class="tabcontent">
						<p>
							No further action is required.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<form id="form-tilt" action="inventory" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'tilt','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								&emsp;&emsp;&emsp;
								<input type="hidden" id="call" name="call" value="gcp">
								<span class="span-form-group-orgid">Org ID</span>
								<input type="text" placeholder="Enter Org Id" id="orgid" name="orgid"
									class="form-group-class-orgid" style="border-radius:5px;"><br><br><br>
								<span class="span-form-group-projids">Folder ID</span>
								<input type="text" placeholder="Folderid" class="form-group-class-projids"
									id="folderids" style="border-radius:5px;" name="folderids"><br><br><br>
								<span class="span-form-group-projids">Allowed Project IDs</span>
								<input type="text" placeholder="Proj1,Proj2,Proj3"
									class="form-group-class-projids" id="projids" style="border-radius:5px;"
									name="projids"><br><br><br>
								<span class="span-form-group-projids">Redis/Memcache Regions</span>
								<input type="text" class="form-group-class-projids" id="redis-reg"
									name="redis-reg" placeholder="us-central1,us-east1..."
									style="border-radius:5px;"><br>
								{% if provider == 'aws' %}
								<input type="hidden" name="gcp-target" id="gcp-target" value="0">
								<div
									style="display: flex; align-items: center; gap: 10px; margin-top: 10px; margin-left: 4px;">
									<span class="span-form-group"
										style="position: relative; margin: 0; padding-right: 0;">Target
										Columns</span>
									<label class="switch">
										<input type="checkbox" id="gcp-target-checkbox"
											name="gcp-target-checkbox" value="0"
											onchange="updateGcpTargetValue(this)">
										<span class="slider round"></span>
									</label>
									<span id="toggle-label-gcp">No</span>
								</div>
								{% endif %}

								{% if provider == 'gcp' %}
								<div class="slider-container">
									<span>AI Summary : </span>
									<label class="switch">
										{% if user_is_allowed %}
										<input type="checkbox" data-target="ai_summary" id="ai_summary"
											name="ai_summary">
										<span class="slider"></span>
										{% else %}
										<input type="checkbox" data-target="ai_summary" id="ai_summary"
											name="ai_summary" disabled>
										<span class="slider"></span>
									</label>
									<span class="text-danger" style="margin-left: 10px;">No Access</span>
									{% endif %}
								</div>
								{% endif %}

								{% if provider == 'aws' %}
								<div class="slider-container">
									<span>AI Summary : </span>
									<label class="switch">
										{% if user_is_allowed %}
										<input type="checkbox" data-target="ai_summary_aws" id="ai_summary_aws"
											name="ai_summary_aws">
										<span class="slider"></span>
										{% else %}
										<input type="checkbox" data-target="ai_summary_aws" id="ai_summary_aws"
											name="ai_summary_aws" disabled>
										<span class="slider"></span>
									</label>
									<span class="text-danger" style="margin-left: 10px;">No Access</span>
									{% endif %}
								</div>
								{% endif %}
								<button id="gcpin" type="submit" class="btn arrow-btn tilt-btn"
									style="border-radius:5px; margin-left: -14px;"
									disabled>Generate</button>
							</div>
							
						</form>
						<form id="form-tilt-looker-gcp" action="looker_url" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'tilt-looker-gcp','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<input type="hidden" id="caller" name="caller" value="gcp">
								<span class="span-form-group-projids">Sheet URL</span>
								<input type="text" placeholder="Enter Google Sheet URL" class="form-group-class-projids"
									id="sheet_url" style="border-radius:5px;" name="sheet_url" required><br><br><br>
								<button id="gcp_looker" name="data"
										type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px;margin-left: -14px;margin-bottom: 15px;">Generate Looker URL</button>
							</div>
						</form>
						<div id="text-block-container-tilt-looker-gcp" style="filter:none"></div>

						<div id="text-block-container-tilt" style="filter:none"></div>
					</div>
				</div>
			</div>

		</div>
	</div>
</div>
