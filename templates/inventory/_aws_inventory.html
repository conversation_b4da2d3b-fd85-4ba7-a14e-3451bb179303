<!--<PERSON><PERSON> invenotry-->
<div id="aws1" class="tab-pane fade">
	<div class="tab-body">
		<div class="content">
			<h3>
				Generate AWS Inventory Workbook for an organisation
				<span class="text" style="color: #29c8e1">by providing ARN</span>

			</h3>
			<div>
				<div class="tab">
					<button class="tablinks" onclick="openCity(event, 'aws1_req')"
						id="defaultOpen">Requirements</button>
					<div id="aws1_req" class="tabcontent">
						<p>
							1. Cloud Run Google Service Account <span class="text"
								style="color: blue"><br><EMAIL></span>
							requires AWS IAM Policy <span class="text"
								style="color: black; font-weight: bold">arn:aws:iam::aws:policy/ReadOnlyAccess</span>
							on AWS project.
						</p>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws1_steps')">Steps</button>
					<div id="aws1_steps" class="tabcontent">

						<p>1. Download <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/hypatia-aws-eks-cfn.yaml"
								target="_blank">AWS Cloudformation file</a> and use the <a
								href="https://docs.google.com/document/d/1NtyMtZHWPc5eaZn5fcXBqYEHDCSyd87NrDFHgsAT6GY/view"
								target="_blank">instructions</a> or <a
								href="https://storage.googleapis.com/hypatia-public-artifacts/pdfs/aws-instructions-new.pdf"
								target="_blank">instructions.pdf</a>.<br>
							2. The Stack should create a IAM Role and will output its ARN. <br>
							3. You need to supply the ARNs in the form.<br>
							4. You can provide comma seperated regions. It is optional.<br>
							5. Click Generate.<br>

						</p>


					</div>
					<button class="tablinks" onclick="openCity(event, 'aws1_out')">Output</button>
					<div id="aws1_out" class="tabcontent">
						<a href="https://docs.google.com/spreadsheets/d/1tGel4HcGTm5KTpiTSB5Ain4GDZP3687dGzwff_WP1C0/view?usp=drive_link"
							target="_blank">Sample Output</a>
						<div class="img-div">
							<img src="./static/assets/images/awsinvent1.png"
								style="margin-left: 0%; margin-top: -8%" width="100%" height="80%">
						</div>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws1_vid')">Video</button>
					<div id="aws1_vid" class="tabcontent">
						<iframe
							src="https://drive.google.com/file/d/12PsnDsvWwl8ArrsXqgoH6wul35J78DHi/preview"
							width="690" height="420" allow="autoplay; fullscreen;"></iframe>
					</div>
					<button class="tablinks" onclick="openCity(event, 'aws1_wn')">What Next?</button>
					<div id="aws1_wn" class="tabcontent">
						<p>
							No further action is required.
						</p>
					</div>
				</div>
				<div class="form-content">
					<div class="card-body"
						style="border-radius:5px; float: left; width : 100%; height : 500px;">
						<div>
							<form id="form-tilt1" action="inventory" method="post"
								enctype="multipart/form-data"
								onsubmit="addUUID(this);  revealText(this, 'tilt1','');">
								<div class="form-group" style="padding-right:0px;margin-top: 10px;">
									<input type="hidden" id="call" name="call" value="aws">
									<span class="span-form-group">ARN</span>
									<input type="text" placeholder="Enter ARN" class="form-group-class"
										id="arn" style="border-radius:5px;" name="arn" required><br><br><br>
									<span class="span-form-group">Regions</span>
									<input type="text" placeholder="*" class="form-group-class" id="regions"
										style="border-radius:5px;" name="regions">

									<br>
									{% if provider == 'gcp' %}
									<input type="hidden" name="aws-target" id="aws-target" value="0">
									<div
										style="display: flex; align-items: center; gap: 10px; margin-top: 10px; margin-left: 4px;">
										<span class="span-form-group"
											style="position: relative; margin: 0; padding-right: 0;">Target
											Columns</span>
										<label class="switch">
											<input type="checkbox" id="aws-target-checkbox"
												name="aws-target-checkbox" value="0"
												onchange="updateAwsTargetValue(this)">
											<span class="slider round"></span>
										</label>
										<span id="toggle-label-aws">No</span>
									</div>
									{% endif %}
									
									{% if provider == 'gcp' %}
									<div class="slider-container">
										<span>AI Summary : </span>
										<label class="switch">
											{% if user_is_allowed %}
											<input type="checkbox" data-target="ai_summary" id="ai_summary"
												name="ai_summary">
											<span class="slider"></span>
											{% else %}
											<input type="checkbox" data-target="ai_summary" id="ai_summary"
												name="ai_summary" disabled>
											<span class="slider"></span>
										</label>
										<span class="text-danger" style="margin-left: 10px;">No
											Access</span>
										{% endif %}
									</div>
									{% endif %}
									
									{% if provider == 'aws' %}
									<div class="slider-container">
										<span>AI Summary : </span>
										<label class="switch">
											{% if user_is_allowed %}
											<input type="checkbox" data-target="ai_summary_aws" id="ai_summary_aws"
												name="ai_summary_aws">
											<span class="slider"></span>
											{% else %}
											<input type="checkbox" data-target="ai_summary_aws" id="ai_summary_aws"
												name="ai_summary_aws" disabled>
											<span class="slider"></span>
										</label>
										<span class="text-danger" style="margin-left: 10px;">No
											Access</span>
										{% endif %}
									</div>
									{% endif %}

									<br><br><br>

									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
									&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;<button id="awsin" name="data"
										type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px;margin-left:-212px; margin-top: -25px;margin-bottom: 15px;">Generate</button>
								</div>
							</form>
							<form id="form-tilt-looker-aws" action="looker_url" method="post" enctype="multipart/form-data"
							onsubmit="addUUID(this);  revealText(this, 'tilt-looker-aws','');">
							<div class="form-group" style="padding-right:0px;margin-top: 10px;">
								<input type="hidden" id="caller" name="caller" value="aws">
								<span class="span-form-group">Sheet URL</span>
								<input type="text" placeholder="Enter Google Sheet URL" class="form-group-class"
									id="sheet_url" style="border-radius:5px;" name="sheet_url" required><br><br><br>
								<button id="aws_looker" name="data"
										type="submit" class="btn arrow-btn tilt-btn"
										style="border-radius:5px;margin-bottom: 15px;">Generate Looker URL</button>
							</div>
						</form>
						<div id="text-block-container-tilt-looker-aws" style="filter:none"></div>
							<div id="text-block-container-tilt1" style="filter:none"></div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
