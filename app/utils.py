import logging
import queue
import time
import sys
import os
import shutil
from functools import wraps
from datetime import datetime
import pytz
from flask import request, current_app, jsonify, send_file
from pubsub import pub
from google.cloud import bigquery

from ai.config_gcp import ALLOWED_USERS

class BigQueryLogger:
    def __init__(self, project_id, dataset_id, table_id):
        self.client = bigquery.Client(project=project_id)
        self.dataset_ref = self.client.dataset(dataset_id)
        self.table = table_id

    def log_request(self, requestor_email, group, subgroup, timestamp, processing_time, hypatia_env, total_cost):
        rows_to_insert = [{
            "requestor_email": requestor_email,     # Already a list
            "Group": group,            
            "SubGroup": subgroup,             # Already a list
            "timestamp": timestamp,                 # Already a list
            "processing_time": processing_time,     # Already a list
            "hypatia_env": hypatia_env,              # Already a list
            "total_cost": total_cost                 # Already a list
        }]
        table_ref = self.dataset_ref.table(self.table)
        logging.info(f"Inserting to BigQuery table {table_ref.path} → {rows_to_insert}")
        errors = None
        try:
            errors = self.client.insert_rows_json(table_ref, rows_to_insert)
            pass
        except Exception as errors:
            logging.info(f"Error inserting rows into BigQuery: {errors}")


def get_path(relative_path):
    """
    Get the absolute path to a resource, works for dev and for PyInstaller.
    """
    if getattr(sys, 'frozen', False):
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    else:
        # In development, the base path is the project's root directory
        base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    
    return os.path.join(base_path, relative_path)

# --- Application State Management ---
class ProcessManager:
    """Manages the state of background processes to prevent concurrency."""
    def __init__(self):
        self.current_pid = None
        self.is_busy = False

    def acquire_lock(self, pid):
        if self.is_busy:
            return False
        self.current_pid = pid
        self.is_busy = True
        return True

    def release_lock(self):
        self.current_pid = None
        self.is_busy = False

    def get_busy_response(self):
        return {"msg": "App is busy, please wait a few minutes", "erc": 0}

# --- Global Instances ---
process_manager = ProcessManager()
sse_queue = queue.Queue()

# Mapping dictionaries
FUNCTION_TO_GROUP = {
    "move_val": "Migration", "handle_m2vm_request": "Migration", "gta_m2vm": "Migration",
    "vmware_m2vm": "Assessment", "process_aws_bill": "Assessment", "buildsheet": "Migration",
    "gcloudcmd": "Migration", "json_to_xl": "Compliance", "compliance": "Compliance",
    "stratozone": "Assessment", "inventory": "Inventory", "glide": "Glide",
    "kube": "Inventory", "m2vm": "Migration"
}
FUNCTION_TO_SUBGROUP = {
    "move_val": "MoveGroup Validation", "buildsheet": "Build Sheet", "gcloudcmd": "VM Commands",
    "json_to_xl": "Firewall Summary", "vmware_m2vm": "VMWare Assessment Sheet",
    "process_aws_bill": "Bill Based Scoping", "handle_m2vm_request": "Gcp Migration Sheet",
    "gta_m2vm": "AWS Migration Sheet"
}

# --- Logging Setup ---
class NoStaticFilter(logging.Filter):
    def filter(self, record):
        return "GET /static/" not in record.getMessage()

def setup_logging():
    """Configures logging to send detailed logs to a file and essential logs to the console."""
    # Get the root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # Prevent adding duplicate handlers
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    # --- File Handler ---
    # This handler writes all INFO and above logs to app.log
    log_file_path = get_path("app.log")
    file_handler = logging.FileHandler(log_file_path)
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(
        "[%(asctime)s] [%(process)d] [%(levelname)s] [%(name)s] %(message)s"
    )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # --- Console Handler ---
    # This handler prints INFO and above logs to the console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter("[%(levelname)s] %(message)s")
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # --- Werkzeug Logger Configuration ---
    # We want to keep the console clean, so we set the Werkzeug logger
    # (which logs every HTTP request) to only show warnings and errors.
    # The FileHandler will still capture its INFO messages.
    werkzeug_logger = logging.getLogger("werkzeug")
    werkzeug_logger.setLevel(logging.WARNING)
    
    # The NoStaticFilter is now less critical since we're only showing warnings,
    # but we'll keep it as a good practice.
    werkzeug_logger.addFilter(NoStaticFilter())

# --- Decorators ---
def error_handler(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logging.error(f"An unexpected error occurred: {str(e)}", exc_info=True)
            send_event('completed', file="",err=str(e), msg="An internal error occurred. Please check the logs.", err_code=1)
            return {"msg": "An internal error occurred. Please check the logs.", "erc": 1}, 205
        finally:
            process_manager.release_lock()
            sse_queue.put(None)
    return wrapper

def user_is_allowed(request):
    user_email = request.headers.get('X-Goog-Authenticated-User-Email', 'Unknown').split(':')[-1]
    return user_email in ALLOWED_USERS


# --- Core Logic ---
def sse_listener(data):
    sse_queue.put(data)

def log_request_to_bigquery(function_name, processing_time, total_cost=0):
    try:
        requestor_email = request.headers.get('X-Goog-Authenticated-User-Email', 'unknown').split(':')[-1]
        env = current_app.config['ENVIRONMENT']
        hypatia_env = 'Development' if env == 'development' else 'Staging' if env == 'staging' else 'Production'
        group = FUNCTION_TO_GROUP.get(function_name, 'Unknown')
        subgroup = str(request.form.get('call', 'Unknown')) if function_name in ["inventory", "kube", "stratozone", "compliance", "glide"] else FUNCTION_TO_SUBGROUP.get(function_name, 'Unknown')
        timestamp = datetime.now(pytz.timezone("Asia/Kolkata")).isoformat()
        total_cost = total_cost if total_cost else 0
        
        bigquery_logger = BigQueryLogger(current_app.config['PROJECT_ID'], current_app.config['BIGQUERY_DATASET'], current_app.config['BIGQUERY_TABLE'])
        bigquery_logger.log_request(requestor_email=[requestor_email], group=[group], subgroup=[subgroup], timestamp=[timestamp], processing_time=[processing_time], hypatia_env=[hypatia_env], total_cost=[total_cost])
        logging.info(f"Logged request for '{function_name}' by '{requestor_email}' to BigQuery.")
    except Exception as e:
        logging.error(f"Failed to log request to BigQuery: {e}", exc_info=True)

def send_event(event_type, **kwargs):
    """Helper function to send events to the SSE queue."""
    data = {'status': event_type}
    data.update(kwargs)
    pub.sendMessage('process_status', data=data)

@error_handler
def process_api_request(func, function_name):
    start_time = time.time()
    uuid = request.form.get('uuid')
    send_event('processing', function=function_name)
    
    project_root = os.path.abspath(os.path.join(current_app.root_path, '..'))
    
    # Pass the send_event function to the wrapped function
    original_file_path, err_msg, msg, err_code = func(request, project_root)
    
    final_file_path = original_file_path

    # If a file was generated successfully, move it to a UUID-named directory
    if uuid and original_file_path and os.path.exists(original_file_path):
        try:
            output_dir = get_path(os.path.join('output', uuid))
            os.makedirs(output_dir, exist_ok=True)
            file_name = os.path.basename(original_file_path)
            final_file_path = os.path.join(output_dir, file_name)
            shutil.move(original_file_path, final_file_path)
            logging.info(f"Moved output file to {final_file_path}")
        except Exception as e:
            logging.error(f"Could not move file to UUID directory: {e}", exc_info=True)
            final_file_path = original_file_path

    processing_time = time.time() - start_time
    logging.info(f"Function '{function_name}' completed in {processing_time:.2f} seconds.")

    if function_name in ["inventory", "kube"]:
        total_cost = err_msg
        if err_code in [1, 2]:
            err_msg = msg
        else:
            err_msg = ""
    else:
        total_cost = 0
    
    log_request_to_bigquery(function_name, processing_time, total_cost)
    
    # Use the final path in the SSE message
    send_event('completed', file=final_file_path, err=err_msg, msg=msg, err_code=err_code)
    
    if err_code in [1, 2]:
        return jsonify({"msg": msg, "error": err_msg}), 205
    
    # If a file exists at the final path, send it. Otherwise, return a message.
    if final_file_path and os.path.exists(final_file_path):
        return send_file(final_file_path, as_attachment=True)
    else:
        return jsonify({"msg": msg or "Processing complete, but no file was generated.", "error": err_msg}), 205
