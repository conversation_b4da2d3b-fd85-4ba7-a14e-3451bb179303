import os

class Config:
    """Flask configuration loaded from environment variables."""
    SECRET_KEY = os.getenv('SECRET_KEY', 'a-very-secret-key-that-should-be-changed')
    MAX_CONTENT_LENGTH = 256 * 1024 * 1024
    
    # BigQuery Config
    PROJECT_ID = os.getenv('PROJECT_ID', 'ce-ps3')
    BIGQUERY_DATASET = os.getenv('BIGQUERY_DATASET', 'hypatia')
    BIGQUERY_TABLE = os.getenv('BIGQUERY_TABLE', 'hypatia_iap_requests')
    
    # App environment: 'development', 'staging', or 'production'
    ENVIRONMENT = os.getenv('ENVIRONMENT', 'production').lower()
    DEBUG = ENVIRONMENT == 'development'
