import os
from flask import Blueprint, render_template, request, jsonify, send_file, redirect, url_for
from ..utils import get_path, user_is_allowed

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def home():
    return redirect(url_for('main.gcp'))

@main_bp.route('/gcp')
def gcp():
    return render_template('index.html',
                         user_is_allowed=user_is_allowed(request),
                         provider='gcp')

@main_bp.route('/aws')
def aws():
    return render_template('index.html',
                         user_is_allowed=user_is_allowed(request),
                         provider='aws')

@main_bp.route('/logs')
def logs_page():
    return render_template('log.html')

@main_bp.route('/logging')
def log_view():
    log_file_path = get_path("app.log")
    html_log_path = get_path("templates/log.html")
    try:
        with open(log_file_path, "r") as f:
            server_content = f.read()
    except FileNotFoundError:
        server_content = "Log file not found."
    try:
        with open(html_log_path, "r") as f:
            process_content = f.read()
    except FileNotFoundError:
        process_content = "Process log file not found."
    return render_template('logging.html', server_content=server_content, process_content=process_content)

@main_bp.route('/dfile')
def download_file():
    file_path = request.args.get('path')
    if file_path and os.path.exists(file_path) and file_path.startswith(get_path('output')):
        return send_file(file_path, as_attachment=True)
    return "Error: Invalid file path or file not found.", 404
