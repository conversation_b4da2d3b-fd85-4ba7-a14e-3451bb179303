function switchCont(invoker) {
    for (let i = 1; i <= 5; i++) {
        const div = document.getElementById('div' + i);
        const button = document.getElementById('buto' + i);
        if (i === invoker) {
            div.style.display = 'block';
            button.classList.add('active');
        } else {
            div.style.display = 'none';
            button.classList.remove('active');
        }
    }
}

function addUUID(form) {
    // This function is not strictly necessary with the new implementation,
    // but we'll keep it for now to avoid breaking any existing logic.
}

function updateTargetValue(checkbox) {
    // Find the hidden input with name="target" and update its value
    const hiddenInput = document.querySelector('input[type="hidden"][name="target"]');
    if (hiddenInput) {
        hiddenInput.value = checkbox.checked ? "1" : "0";
    }
    
    // Update the label
    const label = document.getElementById("toggle-label");
    label.innerText = checkbox.checked ? "Yes" : "No";
    
    console.log("Target toggle updated:", checkbox.checked ? "1" : "0");
}

function revealText(form) {
    const uuid = crypto.randomUUID().replace(/-/g, '');
    let uuidInput = form.elements["uuid"];
    if (!uuidInput) {
        uuidInput = document.createElement("input");
        uuidInput.type = "hidden";
        uuidInput.name = "uuid";
        form.appendChild(uuidInput);
    }
    uuidInput.value = uuid;

    // --- New Loader Implementation ---
    const overlay = document.createElement("div");
    overlay.id = "loadingOverlay";
    overlay.className = "loading-overlay";

    const loaderContainer = document.createElement("div");
    loaderContainer.className = "loader-container";

    const loaderTitle = document.createElement("h4");
    loaderTitle.className = "loader-title";
    loaderTitle.innerText = "Processing your request...";

    const uuidText = document.createElement("p");
    uuidText.className = "loader-uuid";
    uuidText.innerText = "Request ID: " + uuid;

    const loaderAnimation = document.createElement("div");
    loaderAnimation.className = "loader-animation";
    for (let i = 0; i < 5; i++) {
        const bar = document.createElement("div");
        bar.className = "bar";
        loaderAnimation.appendChild(bar);
    }

    const statusText = document.createElement("p");
    statusText.className = "loader-status";
    statusText.innerText = "Initializing...";

    const liveOutputContainer = document.createElement("div");
    liveOutputContainer.className = "live-output-container";
    liveOutputContainer.style.display = "none"; // Hidden by default

    const liveOutputPre = document.createElement("pre");
    liveOutputPre.className = "live-output";
    liveOutputContainer.appendChild(liveOutputPre);

    const timerText = document.createElement("p");
    timerText.className = "loader-timer";
    timerText.innerText = "Time: 0s";

    const closeButton = document.createElement("button");
    closeButton.className = "btn-close";
    closeButton.innerHTML = "&times;";
    closeButton.onclick = () => {
        overlay.remove();
        if (eventSource) {
            eventSource.close();
        }
    };

    loaderContainer.appendChild(closeButton);
    loaderContainer.appendChild(loaderTitle);
    loaderContainer.appendChild(uuidText);
    loaderContainer.appendChild(loaderAnimation);
    loaderContainer.appendChild(statusText);
    loaderContainer.appendChild(liveOutputContainer);
    loaderContainer.appendChild(timerText);
    overlay.appendChild(loaderContainer);
    document.body.appendChild(overlay);

    // --- Timer Implementation ---
    let totalSeconds = 0;
    const timerInterval = setInterval(() => {
        totalSeconds++;
        let timerString;
        if (totalSeconds < 60) {
            timerString = `${totalSeconds}s`;
        } else {
            const minutes = Math.floor(totalSeconds / 60);
            const seconds = totalSeconds % 60;
            timerString = `${minutes}m ${seconds}s`;
        }
        timerText.innerText = `Time: ${timerString}`;
    }, 1000);

    // --- SSE Implementation ---
    const eventSource = new EventSource('/process_status');

    eventSource.onopen = function() {
        statusText.innerText = "Connection established. Waiting for data...";
    };

    eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);

        if (data.status === 'processing') {
            statusText.innerText = `Now processing: ${data.function}...`;
        } else if (data.status === 'log') {
            liveOutputContainer.style.display = "block";
            liveOutputPre.textContent = data.log;
        } else if (data.status === 'completed') {
            // The stream will be closed by the server, which triggers onerror.
            // We just update the UI here.
            statusText.innerText = data.msg || 'Your file is ready for download.';
            if (form.action.includes('looker_url')) {
                const lookerLink = document.createElement('a');
                lookerLink.href = data.file;
                lookerLink.className = 'btn btn-primary';
                lookerLink.innerText = 'Open Looker Dashboard';
                lookerLink.target = '_blank';
                if (data.err) {
                    lookerLink.className = 'btn btn-danger';
                    lookerLink.innerText = 'Error';
                }
                else {
                    lookerLink.target = data.file
                    loaderContainer.appendChild(lookerLink);
                }
            } else if (data.file) {
                const downloadLink = document.createElement('a');
                downloadLink.href = `/dfile?path=${data.file}`;
                downloadLink.className = 'btn btn-primary';
                downloadLink.innerText = 'Download File';
                loaderContainer.appendChild(downloadLink);
            }
            if (data.err) {
                const errorDetails = document.createElement('p');
                errorDetails.className = 'text-danger';
                errorDetails.innerText = `Error: ${data.err}`;
                statusText.after(errorDetails);
            }
            form.reset();
        }
    };

    eventSource.onerror = function() {
        // This event fires when the connection is closed by the server.
        eventSource.close();
        clearInterval(timerInterval);
        loaderAnimation.style.display = 'none'; // Stop the animation
        // liveOutputContainer.style.display = 'none'; // Hide the log container
        loaderTitle.innerText = "Process Finished";
        statusText.innerText = "Process completed. You can now close this window.";
    };
}

function openCity(evt, cityName) {
    var i, tabcontent, tablinks;
    if (document.getElementById(cityName).style.display === "block") {
        document.getElementById(cityName).style.display = "none";
        evt.currentTarget.classList.remove("active");
    } else {
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        document.getElementById(cityName).style.display = "block";
        evt.currentTarget.className += " active";
    }
}

function initCity() {
    var i, tabcontent;
    tabcontent = document.getElementsByClassName("tabcontent");
    for (i = 0; i < tabcontent.length; i++) {
        tabcontent[i].style.display = "none";
    }
}

initCity();

function redirectToLink() {
    window.open("https://docs.google.com/spreadsheets/d/1kq2JisRRh0ePMiEloL1B5oy0dw3fFpvQYGdgwgg4Gjk/edit?usp=drive_link", "_blank");
}

function redirectToLink1() {
    window.open("https://lookerstudio.google.com/reporting/9da7effe-7926-4b42-a441-7b9d3d149395/page/L2qSD", "_blank");
}


function updateAwsTargetValue(checkbox) {
    const awsTarget = document.getElementById('aws-target');
    const toggleLabelAws = document.getElementById('toggle-label-aws');

    if (checkbox.checked) {
        awsTarget.value = 1;
        toggleLabelAws.textContent = 'Yes';
    } else {
        awsTarget.value = 0;
        toggleLabelAws.textContent = 'No';
    }
}



function updateGcpTargetValue(checkbox) {
    const gcpTarget = document.getElementById('gcp-target');
    const toggleLabelGcp = document.getElementById('toggle-label-gcp');

    if (checkbox.checked) {
        gcpTarget.value = 1;
        toggleLabelGcp.textContent = 'Yes';
    } else {
        gcpTarget.value = 0;
        toggleLabelGcp.textContent = 'No';
    }
}

function updateOciTargetValue(checkbox) {
    const ociTarget = document.getElementById('oci-target');
    const toggleLabelOci = document.getElementById('toggle-label-oci');

    if (checkbox.checked) {
        ociTarget.value = 1;
        toggleLabelOci.textContent = 'Yes';
    } else {
        ociTarget.value = 0;
        toggleLabelOci.textContent = 'No';
    }
}




function updateAzureTargetValue(checkbox) {
    const awsTarget = document.getElementById('azure-target');
    const toggleLabelAws = document.getElementById('toggle-label-azure');

    if (checkbox.checked) {
        awsTarget.value = 1;
        toggleLabelAws.textContent = 'Yes';
    } else {
        awsTarget.value = 0;
        toggleLabelAws.textContent = 'No';
    }
}



function updateAzuretoAwsTargetValue(checkbox) {
    const awsTarget = document.getElementById('azure-aws-target');
    const toggleLabelAws = document.getElementById('toggle-label-azure-aws');

    if (checkbox.checked) {
        awsTarget.value = 1;
        toggleLabelAws.textContent = 'Yes';
    } else {
        awsTarget.value = 0;
        toggleLabelAws.textContent = 'No';
    }
}


function checkFields() {
    function checkButtonState(fieldIds, submitButtonId) {
        const isAnyFieldFilled = fieldIds.some(id => document.getElementById(id).value);
        document.getElementById(submitButtonId).disabled = !isAnyFieldFilled;
    }

    checkButtonState(["orgid", "projids", "folderids"], "gcpin");
    checkButtonState(["orgid1", "projids1", "folderids1"], "gcpin1");
}

["orgid", "projids", "folderids", "orgid1", "projids1", "folderids1"].forEach(id => {
    if (document.getElementById(id)) {
        document.getElementById(id).addEventListener("input", checkFields);
    }
});

checkFields();

document.addEventListener('DOMContentLoaded', function() {
    const sliders = document.querySelectorAll('.switch input[type="checkbox"]');
    sliders.forEach(slider => {
        slider.addEventListener('change', function() {
            const targetId = this.getAttribute('data-target');
            const fieldsContainer = document.getElementById(targetId);
            if (fieldsContainer) {
                if (this.checked) {
                    fieldsContainer.classList.add('show');
                } else {
                    fieldsContainer.classList.remove('show');
                }
            }
        });
    });
});
