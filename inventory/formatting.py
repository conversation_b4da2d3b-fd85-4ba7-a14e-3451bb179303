import os
import pandas as pd
import openpyxl
from openpyxl.styles import PatternFill
import re
import logging

from ai.ai_summary_inv import generate_ai_summary
from inventory.constants import aws_sheet_names_to_update, azure_sheet_names_to_update, gcp_to_aws_mappings, azure_to_aws_mappings

logger = logging.getLogger(__name__)

aws_sheet_names_to_update = {
    "CERTIFICATES": [
        "Migration (Yes/No)",
        "ProjectID",
        "Cert Name",
        "Location",
        "CertificateType",
        "Domains",
        "SubjectAlternativeNames",
        "CertMap",
        "CertMapEntry",
        "SelfManagedCertificate",
        "SelfManagedPrivateKey"
    ],
    "EC2": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "HostProject",
        "Movegroup",
        "Status",
        "Target_Region",
        "Zone",
        "Size",
        "Image",
        "ImageProject",
        "Disk",
        "DiskType",
        "SnapshotPolicy",
        "Confidential VM service",
        "Shielded VM",
        "VPC",
        "Subnet",
        "IP",
        "ExternalIP",
        "InternalIP",
        "NetworkTag",
        "Access",
        "SA",
        "Labels"
    ],
    "ELASTICACHE_CLUSTER": [
        "Migration (Yes/No)",
        "ProjectID",
        "InstanceID",
        "Target_Region",
        "Zone",
        "Nodetype",
        "RedisVersion",
        "Replicas(per shard)",
        "Connections",
        "PSARange",
        "Enable IAM AUTH",
        "EnableInTransitEncryption",
        "Snapshot Interval"
    ],
    "RDS": [
        "Migration (Yes/No)",
        "ProjectID",
        "Instance Name",
        "HostProject",
        "Target_Region",
        "Zone-Primary",
        "Zone-Secondary(optional)",
        "Edition",
        "DB Version",
        "CPU",
        "Memory(GB)",
        "Storage(GB)",
        "High Availability",
        "Replica",
        "PrivateNetwork",
        "PSARange",
        "Authorizednetwork"
    ],
    "S3": [
        "Migration (Yes/No)",
        "ProjectID",
        "Bucket Name",
        "Target_Region",  # Updated to match actual header
        "Movegroup",
        "Status",
        "Public Access?",
        "IAM",
        "Scope",
        "Access Control",
        "Retention",
        "Retention Period",
        "Object Versioning",
        "Max. versions per object",
        "Expire non-current versions after",
        "Soft delete policy (For data recovery)",
        "Lifecycle Policy Rule1",
        "Lifecycle Policy Rule2",
        "Lifecycle Policy Rule3",
        "Storage Class",
        "Labels"
    ],
    "LAMBDA": [
        "Migration (Yes/No)",
        "ProjectID",
        "ProjectName",
        "ServiceName",
        "Target_Region",
        "HostProject",
        "SecretProject",
        "Generation", 
        "Authentication",
        "IAP",
        "ServiceAccountEmail",
        "SecurityLevel",
        "SecretEnvVariables",
        "EnvVariables",
        "HTTPS Trigger",
        "VPCConnector",
        "VPCConnectorEgress",
        "HostVPC",
        "Subnet",
        "Ingress Settings",
        "Session Affinity",
        "Container Port",
        "Runtime",
        "EntryPoint",
        "Container Name",
        "Target_Memory",
        "Target_Timeout",
        "Concurrency",
        "MinInstances",
        "MaxInstances",
        "CloudSQL connections",
        "DockerRegistry",
        "SourceUploadUrl",
        "SourceBucket",
        "SourceObject",
        "Labels",
        "Billing",
        "TriggerType",
        "EventTriggers"
    ],
    "SNS": [
        "Migration (Yes/No)",
        "ProjectID",
        "TopicName",
        "SubscriptionName",
        "DeliveryType",
        "MessageRetentionDuration",
        "ExpirationPeriod",
        "RetryPolicy",
        "MaxDeliveryAttempt",
        "DLQ Enable? (yes/no)",
        "DLQ TopicName",
        "ACK Deadline (seconds)",
        "enable_message_ordering",
        "exactly_once_delivery"
    ],
    "ECS_SERVICE": [
        "Migration (Yes/No)",
        "ProjectID",
        "ProjectName",
        "ServiceName",
        "Target_Region",
        "HostProject",
        "SecretProject",
        "Container Image URL",
        "Generation:2", 
        "Authentication",
        "ServiceAccountEmail",
        "SecurityLevel",
        "SecretEnvVariables",
        "EnvVariables",
        "HTTPS Trigger",
        "VPCConnector",
        "VPCConnectorEgress",
        "HostVPC",
        "Subnet",
        "Ingress Settings",
        "Session Affinity",
        "Container Port",
        "Runtime",
        "EntryPoint",
        "Container Name",
        "Memory",
        "Timeout",
        "Concurrency",
        "MinInstances",
        "MaxInstances",
        "CloudSQL connections",
        "DockerRegistry",
        "SourceUploadUrl",
        "SourceBucket",
        "SourceObject",
        "Labels",
        "Billing",
        "TriggerType",
        "EventTriggers"
    ],
    "SQS": [
        "Migration (Yes/No)",
        "ProjectID",
        "QueueName",
        "DeliveryType",
        "MessageRetentionDuration",
        "ExpirationPeriod",
        "RetryPolicy",
        "MaxDeliveryAttempt",
        "DLQ Enable?",
        "DLQ TopicName",
        "ACK Deadline (seconds)",
        "enable_message_ordering",
        "exactly_once_delivery"
    ],
    "ELBV1" : [
        "Migration (Yes/No)",
        "ProjectID",
        "Target_Region",
        "Scope",
        "LB scheme",
        "LB Type",
        "LB Name",
        "Fe Name",
        "Fe Protocol",
        "Fe Ports",
        "IP Type",
        "Static IP Address",
        "FE IP address",
        "Network Tier",
        "SSLCertificate",
        "SSLCertificate Type",
        "Certificate Map",
        "SSL Policy",
        "BE Service Name",
        "Backend Type",
        "BE region",
        "BE protocol",
        "BE ports",
        "BE Balancing Mode",
        "Http timeout",
        "Session affinity",
        "ConnectionDrainTimeoutSec",
        "Enable CDN",
        "Health check protocol",
        "Health check ports",
        "HealthCheckIntervalSec",
        "URL map",
        "HostRules",
        "Path matchers",
        "SecurityPolicy",
        "FirewallRuleTag",
        "LoggingEnabled",
        "LoggingSampleRate",
        "Network",
        "Subnetwork",
        "MaxRatePerEndpoint",
        "FailoverPolicyEnabled"
    ],
    "ELBV2" : [
        "Migration (Yes/No)",
        "ProjectID",
        "Target_Region",
        "Scope",
        "LB scheme",
        "LB Type",
        "LB Name",
        "Fe Name",
        "Fe Protocol",
        "Fe Ports",
        "IP Type",
        "Static IP Address",
        "FE IP address",
        "Network Tier",
        "SSLCertificate",
        "SSLCertificate Type",
        "Certificate Map",
        "SSL Policy",
        "BE Service Name",
        "Backend Type",
        "BE region",
        "BE protocol",
        "BE ports",
        "BE Balancing Mode",
        "Http timeout",
        "Session affinity",
        "ConnectionDrainTimeoutSec",
        "Enable CDN",
        "Health check protocol",
        "Health check ports",
        "HealthCheckIntervalSec",
        "URL map",
        "HostRules",
        "Path matchers",
        "SecurityPolicy",
        "FirewallRuleTag",
        "LoggingEnabled",
        "LoggingSampleRate",
        "Network",
        "Subnetwork",
        "MaxRatePerEndpoint",
        "FailoverPolicyEnabled"
    ]
}

def consolidate_azure_tags(df):
    """
    Takes a DataFrame and consolidates multi-row tags into a single row per resource,
    preserving all original columns and rows, including those without tags.

    Args:
        df (pd.DataFrame): The input DataFrame.

    Returns:
        pd.DataFrame: A new DataFrame with tags consolidated, or the original DataFrame.
    """
    logger.debug("Checking for tag columns to consolidate...")
    # Define the columns that uniquely identify a resource.
    identifier_cols = ['Subscription', 'Resource Group', 'Name', 'Location']
    
    # Filter out identifier columns that might not exist in the dataframe to avoid errors
    existing_identifier_cols = [col for col in identifier_cols if col in df.columns]

    # If no identifier columns are found, we can't group resources, so we return the original df.
    if not existing_identifier_cols:
        logger.warning("No identifier columns found (e.g., 'ResourceName'). Cannot perform tag consolidation.")
        return df

    # Check for required tag columns. If not present, no action is taken.
    if 'Tag Name' not in df.columns or 'Tag Value' not in df.columns:
        logger.debug("Required 'Tag Name' or 'Tag Value' columns not found in this sheet. Skipping consolidation.")
        return df

    # Ensure consistent data types for merge keys to prevent merge failures.
    # This is a critical step to ensure resources without tags are not dropped.
    for col in existing_identifier_cols:
        df[col] = df[col].astype(str).replace('nan', '')

    # 1. Create a base DataFrame with unique resources and all original columns *except* tag-specific ones.
    # This preserves all columns and ensures rows without tags are kept.
    base_df = df.drop(columns=['Tag Name', 'Tag Value'], errors='ignore').drop_duplicates(subset=existing_identifier_cols)
    if base_df.empty and not df.empty:
        logger.warning("Could not identify unique resources based on identifier columns. Returning original structure.")
        return df

    # 2. Isolate the rows that actually have tags to pivot them.
    tags_df = df.dropna(subset=['Tag Name'])
    if tags_df.empty:
        logger.debug("No rows with tags found. Returning de-duplicated data without pivoting.")
        return base_df.fillna('') # Ensure NaNs from empty tags are cleaned up.

    # 3. Pivot the tags DataFrame.
    try:
        logger.debug("Found tag columns. Attempting to pivot data...")
        pivot_df = tags_df.pivot_table(
            index=existing_identifier_cols,
            columns='Tag Name',
            values='Tag Value',
            aggfunc='first'
        ).reset_index()
        pivot_df.columns.name = None
    except Exception as e:
        logger.error(f"Failed to pivot data on this sheet. Error: {e}")
        return df # Return original dataframe if pivot fails

    # 4. Merge the pivoted tags back to the base DataFrame.
    # A 'left' merge ensures all original unique resources from base_df are kept.
    logger.debug("Merging pivoted tags back to the base data...")
    final_df = pd.merge(base_df, pivot_df, on=existing_identifier_cols, how='left')

    # Fill NaN values that result from the merge (for resources without tags) with empty strings.
    final_df.fillna('', inplace=True)
    
    logger.debug("Tag consolidation successful, preserving all rows and columns.")
    return final_df

def get_project_counts_from_sheet(sheet_df,platform):
    # Assuming the project name is in the first column of each sheet
    if platform == 'aws':
        project_counts = sheet_df.iloc[:, 1].value_counts()
    else:
        project_counts = sheet_df.iloc[:, 0].value_counts()
    return project_counts

def create_summary_sheet(file_path, platform):

    # Read all sheets from the Excel file into a dictionary of DataFrames
    all_sheets = pd.read_excel(file_path, sheet_name=None)
    print("Reached summary sheet generation...")

    project_counts_dict = {}

    # Loop through each sheet and extract project counts
    for sheet_name, sheet_df in all_sheets.items():
        if (platform == 'azure' and (sheet_name in ['Advisory','Advisor','Subscriptions', 'Policy', 'Overview'])) or (platform == 'gcp' and (sheet_name == 'folder_metadata' or sheet_name == 'org_policies' or sheet_name == 'IAM policy' or sheet_name == 'project_metadata')) :
            continue

        sheet_counts = get_project_counts_from_sheet(sheet_df, platform)
        project_counts_dict[sheet_name] = sheet_counts

    # Replace NaN values with 0 (where a project doesn't appear in a sheet)
    summary_df = pd.DataFrame(project_counts_dict).fillna(0).astype(int)

    # Add row-wise total
    summary_df.loc['Total'] = summary_df.sum(axis=0)

    # Set the index label depending on the platform
    if platform == 'azure':
        index_label = 'Subscription'
    elif platform == 'gcp':
        index_label = 'Project'
    elif platform == 'aws':
        index_label = 'Region'
    elif platform == 'oci':
        index_label = 'compartment_name'

    # Write the summary sheet first
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl') as writer:
        summary_df.to_excel(writer, sheet_name='AA Summary', index_label=index_label)

    # Append all other sheets to the file
    with pd.ExcelWriter('./summary_temp.xlsx', engine='openpyxl', mode='a') as writer:
        for sheet_name, sheet_df in all_sheets.items():

            if sheet_name != 'All Summary':  # Skip the summary sheet
                sheet_df.to_excel(writer, sheet_name=sheet_name, index=False)

    # Replace original file
    os.replace('summary_temp.xlsx', file_path)


def add_columns_to_sheet(file_path, sheet_to_column_mapping):
    """
    Adds specified columns to specific sheets in an Excel file and colors
    the new headers green.
    """
    try:
        workbook = openpyxl.load_workbook(file_path)
        # Define the green fill style once for efficiency
        green_fill = PatternFill(start_color='C6E0B4', end_color='C6E0B4', fill_type='solid')
        sheets_updated = []

        for sheet_name, columns in sheet_to_column_mapping.items():
            if sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                start_col = sheet.max_column + 1
                
                for i, column_name in enumerate(columns):
                    # Get the cell object while setting its value
                    new_header_cell = sheet.cell(row=1, column=start_col + i, value=column_name)
                    # Apply the green fill to the new header cell
                    new_header_cell.fill = green_fill
                
                sheets_updated.append(sheet_name)

        if sheets_updated:
            workbook.save(file_path)
            logger.info(f"Added and formatted target columns in sheets: {', '.join(sheets_updated)}.")
        else:
            logger.warning(f"None of the target sheets ({', '.join(sheet_to_column_mapping.keys())}) were found.")
            
    except Exception as e:
        logger.exception("Failed to add and format target columns: " + str(e))


def add_target_columns_azure(file_path):
    add_columns_to_sheet(file_path, azure_sheet_names_to_update)


def add_target_name_id(file_path):
    add_columns_to_sheet(file_path, aws_sheet_names_to_update)


def gcp_to_aws_sheet_names_to_update(file_path):
    add_columns_to_sheet(file_path, gcp_to_aws_mappings)


def azure_to_aws_sheet_names_to_update(file_path):
    add_columns_to_sheet(file_path, azure_to_aws_mappings)
        

def main(out_path, platform, ai_summary_platform='gcp', ai_summary=False, azure_target=False, gcp_target=False, azure_to_aws_target=False):
    total_cost = 0
    if platform == 'azure':
        if azure_target:
            add_target_columns_azure(out_path)
    elif platform == 'gcp':
        if gcp_target:
            gcp_to_aws_sheet_names_to_update(out_path)
    if platform == 'azure':
        if azure_to_aws_target:
            azure_to_aws_sheet_names_to_update(out_path)
        try:
            logger.info("Consolidating tags on all applicable sheets...")
            all_sheets = pd.read_excel(out_path, sheet_name=None)

            # Iterate through all sheets and apply consolidation where possible
            for sheet_name in list(all_sheets.keys()):
                try:
                    logger.debug(f"Processing sheet: '{sheet_name}'")
                    consolidated_df = consolidate_azure_tags(all_sheets[sheet_name])
                    # Update the dictionary with the processed DataFrame
                    all_sheets[sheet_name] = consolidated_df
                except Exception as e:
                    logger.error(f"Error processing sheet '{sheet_name}': {e}")

            # Write all sheets (original and consolidated) back to the file
            with pd.ExcelWriter(out_path, engine='openpyxl') as writer:
                for sheet_name, df_sheet in all_sheets.items():
                    df_sheet.to_excel(writer, sheet_name=sheet_name, index=False)
            logger.info("Successfully processed all sheets for tag consolidation and updated the file.")

        except Exception as e:
            logger.error(f"An error occurred during the tag consolidation step: {e}")
            pass
    try:
        create_summary_sheet(out_path,platform)
    except:
        pass
    if ai_summary:
        try:
            out_path, total_cost = generate_ai_summary('', out_path, platform.upper(),ai_summary,ai_summary_platform.upper())
            logger.info(f"Total cost: {total_cost}")
        except Exception as e:
            logger.error(f"Failed to generate AI summary: {e}")

    #Do the necessary editing
    wb = openpyxl.load_workbook(out_path)
    sorted_worksheets = sorted(wb.worksheets, key=lambda x: x.title)
    for i, worksheet in enumerate(sorted_worksheets):
        wb.move_sheet(worksheet, i+1)

    # Define fill colors once for efficiency
    grey_fill = PatternFill('solid', fgColor='a9a9a9')
    green_fill = PatternFill('solid', fgColor='C6E0B4') # A standard light green

    if platform == 'aws':
        sheet_names_to_update = aws_sheet_names_to_update
    elif platform == 'azure':
        if azure_to_aws_target:
            sheet_names_to_update = azure_to_aws_mappings
        else:
            sheet_names_to_update = azure_sheet_names_to_update
    elif platform == 'gcp':
        sheet_names_to_update = gcp_to_aws_mappings
    else:
        sheet_names_to_update = {}


    for sheet in wb.worksheets:
        # Get the list of headers to color green for the current sheet.
        # Use .get() to return an empty list if the sheet is not in the dictionary.
        headers_to_color_green = sheet_names_to_update.get(sheet.title, [])
        
        # Debug logging to identify issues
        logger.debug(f"Processing sheet: '{sheet.title}'")
        logger.debug(f"Headers to color green: {headers_to_color_green}")
        
        # Get actual headers in the sheet
        actual_headers = [str(cell.value).strip() if cell.value else '' for cell in sheet[1]]
        logger.debug(f"Actual headers in sheet: {actual_headers}")

        # IMPROVED HEADER COLORING LOGIC
        # Process each header cell individually and determine its color
        for cell in sheet[1]:  # First row contains headers
            if cell.value:
                header_value = str(cell.value).strip()
                if header_value in headers_to_color_green:
                    cell.fill = green_fill
                    logger.debug(f"✓ Colored '{header_value}' green in sheet '{sheet.title}'")
                else:
                    cell.fill = grey_fill
                    logger.debug(f"✗ Colored '{header_value}' grey in sheet '{sheet.title}' (not in target list)")
            else:
                cell.fill = grey_fill

        # Dynamically edits the column width according to the content pasted there.
        dims = {}
        for row in sheet.rows:
            for cell in row:
                if cell.value:
                    dims[cell.column_letter] = max((dims.get(cell.column_letter, 0), len(str(cell.value))))
        for col, value in dims.items():
            sheet.column_dimensions[col].width = value + 3

        # Autofilter for all headings
        sheet.auto_filter.ref = sheet.dimensions

        # Remove ; and replace with new line
        for row in sheet.iter_rows():
            for cell in row:
                # Check if the cell value is a string
                if isinstance(cell.value, str):
                # Use re.sub to replace the text in the cell
                    cell.value = re.sub(r' ; ', chr(13) + chr(10), cell.value)

                if cell.value == 'Total':
                    for cells_in_row in row:
                        cells_in_row.fill = PatternFill('solid', fgColor='c9c9c9')

    wb.save(out_path)
    logger.debug('completed.')
    return total_cost
