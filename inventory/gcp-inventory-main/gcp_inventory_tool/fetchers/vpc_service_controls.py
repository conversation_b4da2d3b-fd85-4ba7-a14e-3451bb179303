# --- File: gcp_inventory_tool/fetchers/vpc_service_controls.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.api_core import exceptions as api_exceptions
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class VPCServiceControlsFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPC Service Controls (Service Perimeter) details for an organization.
    VPC Service Controls are organization-level resources managed through Access Context Manager.
    """
    SERVICE_NAME = "vpc_service_controls"  # Unique key for this service type

    def _get_organization_id(self, service, project_id: str) -> Optional[str]:
        """
        Retrieves the organization ID for the given project.
        VPC Service Controls are organization-level resources.
        """
        try:
            # Get project details to find the organization
            project_service = build('cloudresourcemanager', 'v1', credentials=service._http.credentials)
            project = project_service.projects().get(projectId=project_id).execute()
            
            if 'parent' in project and project['parent']['type'] == 'organization':
                return project['parent']['id']
            else:
                logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Project is not under an organization. VPC Service Controls require organization-level access.")
                return None
                
        except HttpError as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to get organization ID: {e}")
            return None

    def _list_access_policies(self, service, organization_id: str) -> List[Dict[str, Any]]:
        """
        Lists all access policies for the organization.
        """
        try:
            policies = []
            request = service.accessPolicies().list(parent=f"organizations/{organization_id}")
            
            while request is not None:
                response = request.execute()
                if 'accessPolicies' in response:
                    policies.extend(response['accessPolicies'])
                request = service.accessPolicies().list_next(request, response)
            
            return policies
            
        except HttpError as e:
            logger.error(f"[{organization_id}][{self.SERVICE_NAME}] Failed to list access policies: {e}")
            return []

    def _list_service_perimeters(self, service, policy_name: str) -> List[Dict[str, Any]]:
        """
        Lists all service perimeters for a given access policy.
        """
        try:
            perimeters = []
            request = service.accessPolicies().servicePerimeters().list(parent=policy_name)

            while request is not None:
                response = request.execute()
                if 'servicePerimeters' in response:
                    perimeters.extend(response['servicePerimeters'])
                request = service.accessPolicies().servicePerimeters().list_next(request, response)

            return perimeters

        except HttpError as e:
            logger.error(f"[{policy_name}][{self.SERVICE_NAME}] Failed to list service perimeters: {e}")
            return []

    def _list_access_levels(self, service, policy_name: str) -> List[Dict[str, Any]]:
        """
        Lists all access levels for a given access policy.
        """
        try:
            access_levels = []
            request = service.accessPolicies().accessLevels().list(parent=policy_name)

            while request is not None:
                response = request.execute()
                if 'accessLevels' in response:
                    access_levels.extend(response['accessLevels'])
                request = service.accessPolicies().accessLevels().list_next(request, response)

            return access_levels

        except HttpError as e:
            logger.error(f"[{policy_name}][{self.SERVICE_NAME}] Failed to list access levels: {e}")
            return []

    def _get_access_level_details(self, access_levels: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Creates a mapping of access level names to their details.
        """
        access_level_map = {}
        for level in access_levels:
            name = level.get('name', '')
            access_level_map[name] = {
                'title': level.get('title', ''),
                'description': level.get('description', ''),
                'basic': level.get('basic', {}),
                'custom': level.get('custom', {}),
                'createTime': level.get('createTime', ''),
                'updateTime': level.get('updateTime', '')
            }
        return access_level_map

    def _extract_policy_summary(self, policies: List[Dict[str, Any]], policy_type: str) -> Dict[str, Any]:
        """
        Extracts summary information from ingress or egress policies.
        """
        if not policies:
            return {"count": 0, "hasIdentityBasedAccess": False, "hasDeviceBasedAccess": False}

        has_identity_based = False
        has_device_based = False

        for policy in policies:
            # Check for identity-based access (from/to with identities)
            if policy_type == "ingress":
                from_clause = policy.get("ingressFrom", {})
                if from_clause.get("identities") or from_clause.get("identityType"):
                    has_identity_based = True
            else:  # egress
                to_clause = policy.get("egressTo", {})
                if to_clause.get("resources"):
                    has_device_based = True

            # Check for device-based access (access levels)
            sources = policy.get("ingressFrom", {}).get("sources", []) if policy_type == "ingress" else []
            for source in sources:
                if source.get("accessLevel"):
                    has_device_based = True

        return {
            "count": len(policies),
            "hasIdentityBasedAccess": has_identity_based,
            "hasDeviceBasedAccess": has_device_based
        }

    def _extract_perimeter_info(self, perimeter: Dict[str, Any], project_id: str, policy_name: str, access_level_details: Dict[str, Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extracts and formats service perimeter information.
        """
        # Extract basic information
        name = perimeter.get('name', '')
        title = perimeter.get('title', '')
        description = perimeter.get('description', '')
        create_time = perimeter.get('createTime', '')
        update_time = perimeter.get('updateTime', '')
        
        # Extract perimeter type
        perimeter_type = perimeter.get('perimeterType', 'PERIMETER_TYPE_REGULAR')
        
        # Extract status information
        status = perimeter.get('status', {})
        resources = status.get('resources', [])
        restricted_services = status.get('restrictedServices', [])
        access_levels = status.get('accessLevels', [])
        vpc_accessible_services = status.get('vpcAccessibleServices', {})
        
        # Extract spec information (for dry-run configurations)
        spec = perimeter.get('spec', {})
        spec_resources = spec.get('resources', [])
        spec_restricted_services = spec.get('restrictedServices', [])
        spec_access_levels = spec.get('accessLevels', [])
        spec_vpc_accessible_services = spec.get('vpcAccessibleServices', {})
        
        # Extract ingress and egress policies
        ingress_policies = status.get('ingressPolicies', [])
        egress_policies = status.get('egressPolicies', [])
        spec_ingress_policies = spec.get('ingressPolicies', [])
        spec_egress_policies = spec.get('egressPolicies', [])

        # Process access level details if provided
        access_level_info = []
        if access_level_details:
            for level_name in access_levels:
                if level_name in access_level_details:
                    level_detail = access_level_details[level_name]
                    access_level_info.append({
                        'name': level_name,
                        'title': level_detail.get('title', ''),
                        'description': level_detail.get('description', ''),
                        'hasBasicConditions': bool(level_detail.get('basic')),
                        'hasCustomConditions': bool(level_detail.get('custom'))
                    })

        # Extract project numbers from resources
        project_numbers = []
        for resource in resources:
            if resource.startswith('projects/') and resource.split('/')[1].isdigit():
                project_numbers.append(resource.split('/')[1])

        # Get policy summaries
        ingress_summary = self._extract_policy_summary(ingress_policies, "ingress")
        egress_summary = self._extract_policy_summary(egress_policies, "egress")
        spec_ingress_summary = self._extract_policy_summary(spec_ingress_policies, "ingress")
        spec_egress_summary = self._extract_policy_summary(spec_egress_policies, "egress")

        return {
            "ProjectID": project_id,
            "Name": title or name.split('/')[-1] if name else '',
            "FullName": name,
            "Description": description,
            "PerimeterType": perimeter_type,
            "CreateTime": create_time,
            "UpdateTime": update_time,
            "PolicyName": policy_name,
            
            # Status (current configuration)
            "Resources": resources,
            "ResourceCount": len(resources),
            "RestrictedServices": restricted_services,
            "RestrictedServicesCount": len(restricted_services),
            "AccessLevels": access_levels,
            "AccessLevelsCount": len(access_levels),
            "AccessLevelDetails": access_level_info,
            "ProjectNumbers": project_numbers,
            "ProjectNumbersCount": len(project_numbers),
            
            # VPC Accessible Services
            "VPCAccessibleServicesEnabled": vpc_accessible_services.get('enableRestriction', False),
            "VPCAllowedServices": vpc_accessible_services.get('allowedServices', []),
            "VPCAllowedServicesCount": len(vpc_accessible_services.get('allowedServices', [])),
            
            # Ingress/Egress Policies
            "IngressPolicies": ingress_policies,
            "IngressPoliciesCount": len(ingress_policies),
            "IngressHasIdentityBasedAccess": ingress_summary["hasIdentityBasedAccess"],
            "IngressHasDeviceBasedAccess": ingress_summary["hasDeviceBasedAccess"],
            "EgressPolicies": egress_policies,
            "EgressPoliciesCount": len(egress_policies),
            "EgressHasIdentityBasedAccess": egress_summary["hasIdentityBasedAccess"],
            "EgressHasDeviceBasedAccess": egress_summary["hasDeviceBasedAccess"],
            
            # Spec (dry-run configuration)
            "SpecResources": spec_resources,
            "SpecResourceCount": len(spec_resources),
            "SpecRestrictedServices": spec_restricted_services,
            "SpecRestrictedServicesCount": len(spec_restricted_services),
            "SpecAccessLevels": spec_access_levels,
            "SpecAccessLevelsCount": len(spec_access_levels),
            
            # Spec VPC Accessible Services
            "SpecVPCAccessibleServicesEnabled": spec_vpc_accessible_services.get('enableRestriction', False),
            "SpecVPCAllowedServices": spec_vpc_accessible_services.get('allowedServices', []),
            "SpecVPCAllowedServicesCount": len(spec_vpc_accessible_services.get('allowedServices', [])),
            
            # Spec Ingress/Egress Policies
            "SpecIngressPolicies": spec_ingress_policies,
            "SpecIngressPoliciesCount": len(spec_ingress_policies),
            "SpecIngressHasIdentityBasedAccess": spec_ingress_summary["hasIdentityBasedAccess"],
            "SpecIngressHasDeviceBasedAccess": spec_ingress_summary["hasDeviceBasedAccess"],
            "SpecEgressPolicies": spec_egress_policies,
            "SpecEgressPoliciesCount": len(spec_egress_policies),
            "SpecEgressHasIdentityBasedAccess": spec_egress_summary["hasIdentityBasedAccess"],
            "SpecEgressHasDeviceBasedAccess": spec_egress_summary["hasDeviceBasedAccess"],
            
            # Additional metadata
            "HasDryRunConfig": bool(spec),
            "service": self.SERVICE_NAME
        }

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPC Service Controls (Service Perimeters) accessible from the project's organization.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Service Controls fetch...")
        inventory = []

        try:
            # Build the Access Context Manager service
            service = build('accesscontextmanager', 'v1', credentials=credentials)
            
            # Get organization ID from project
            organization_id = self._get_organization_id(service, project_id)
            if not organization_id:
                logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Could not determine organization ID. Skipping VPC Service Controls fetch.")
                return []

            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found organization ID: {organization_id}")

            # List access policies for the organization
            access_policies = self._list_access_policies(service, organization_id)
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found {len(access_policies)} access policies")

            # For each access policy, list service perimeters and access levels
            for policy in access_policies:
                policy_name = policy.get('name', '')
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing policy: {policy_name}")

                # Fetch access levels for this policy
                access_levels = self._list_access_levels(service, policy_name)
                access_level_details = self._get_access_level_details(access_levels)
                logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found {len(access_levels)} access levels in policy {policy_name}")

                perimeters = self._list_service_perimeters(service, policy_name)
                logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found {len(perimeters)} service perimeters in policy {policy_name}")

                # Process each service perimeter
                for perimeter in perimeters:
                    perimeter_info = self._extract_perimeter_info(perimeter, project_id, policy_name, access_level_details)
                    inventory.append(perimeter_info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied accessing Access Context Manager API. Ensure API is enabled and necessary roles granted (e.g., Access Context Manager Reader). Details: {e}")
            return []
        except api_exceptions.NotFound as e:
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Access Context Manager API might not be enabled or organization not found. Details: {e}")
            return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch VPC Service Controls: {e}", exc_info=True)
            return []

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Service Controls fetch. Found {len(inventory)} service perimeters.")
        return inventory
