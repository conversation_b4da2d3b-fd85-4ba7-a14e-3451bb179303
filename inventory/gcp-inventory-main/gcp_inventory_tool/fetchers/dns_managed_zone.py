# # --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---
# import logging
# from typing import List, Dict, Any, Optional
# from google.oauth2.credentials import Credentials
# from google.cloud import dns
# from google.api_core import exceptions as api_exceptions

# from ..utils.resource_name import get_resource_name
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')


# class DnsManagedZoneFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud DNS Managed Zone details for a project.
#     """
#     SERVICE_NAME = "dns_managed_zone" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Cloud DNS Managed Zones in the specified project.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud DNS Managed Zone fetch...")
#         inventory = []
#         client = dns.ManagedZonesClient(credentials=credentials)

#         try:
#             zones = client.list_managed_zones(project=project_id)
#             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managed zones...")

#             for zone in zones:
#                 # Prepare info dictionary based on PowerShell script's fields
#                 info = {
#                     "Project": project_id,
#                     "Name": zone.name,
#                     "Description": zone.description,
#                     "Visibility": str(zone.visibility), # PUBLIC or PRIVATE
#                     "Networks": [], # Populated below (for private zones)
#                     "Forwarders": [], # Populated below (for forwarding zones)
#                     "CreationTime": zone.creation_time,
#                     "DnsName": zone.dns_name, # Added field
#                     "NameServers": list(zone.name_servers) if zone.name_servers else [], # Added field
#                     "DnssecEnabled": zone.dnssec_config.state == dns.types.DnsKeySpec.KeyType.KEY_SIGNING if zone.dnssec_config else False, # Added field
#                     "LoggingEnabled": zone.logging_config.enable_logging if zone.logging_config else False, # Added field
#                     "service": self.SERVICE_NAME
#                 }

#                 # Private Zone Network Config
#                 if zone.properties.privateVisibilityConfig and zone.properties.privateVisibilityConfig.networks:
#                     info["Networks"] = [
#                         get_resource_name(net.network_url)
#                         for net in zone.properties.privateVisibilityConfig.networks if net.network_url
#                     ]

#                 # Forwarding Config
#                 if zone.forwarding_config and zone.forwarding_config.target_name_servers:
#                     info["Forwarders"] = [
#                         ns.ipv4_address
#                         for ns in zone.forwarding_config.target_name_servers if ns.ipv4_address
#                         # Add ipv6_address if needed: ns.ipv6_address
#                     ]

#                 inventory.append(info)

#         except api_exceptions.Forbidden as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing DNS zones or accessing DNS API. Ensure API is enabled and necessary roles granted (e.g., 'DNS Reader'). Details: {e}")
#             return [] # Return empty list on permission errors
#         except api_exceptions.NotFound as e:
#              # This might indicate the API isn't enabled or project has no DNS zones
#              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud DNS API might not be enabled or project not found / no zones exist. Details: {e}")
#              return []
#         except Exception as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process DNS Managed Zones: {e}", exc_info=True)
#             return [] # Fail gracefully
#         finally:
#             try:
#                 client.transport.close()
#             except Exception:
#                 pass

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud DNS Managed Zone fetch. Found {len(inventory)} zones.")
#         return inventory

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---

# --- File: gcp_inventory_tool/fetchers/dns_managed_zone.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import dns
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class DnsManagedZoneFetcher(ServiceFetcher):
    """
    Fetches Google Cloud DNS Managed Zone details for a project.
    """
    SERVICE_NAME = "dns_managed_zone" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud DNS Managed Zones in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud DNS Managed Zone fetch...")
        inventory = []
        
        # Fix: Use the correct DNS client
        client = dns.Client(project=project_id, credentials=credentials)

        try:
            # Fix: Use the correct method to list zones
            zones = client.list_zones()
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managed zones...")

            for zone in zones:
                # Access the zone properties from _properties attribute
                zone._properties = getattr(zone, '_properties', {})
                logger.debug(f"Zone properties: {zone._properties}")

                # Determine visibility from the properties
                visibility = zone._properties.get('visibility', 'public').upper()

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": zone.name,
                    "Description": zone._properties.get('description', ''),
                    "Visibility": visibility,
                    "Networks": [], # Will be populated below for private zones
                    "Forwarders": [], # Will be populated below for forwarding zones
                    "CreationTime": zone._properties.get('creationTime').isoformat() if zone._properties.get('creationTime') else None,
                    "DnsName": zone.dns_name,
                    "NameServers": zone._properties.get('nameServers', []),
                    "DnssecEnabled": False,  # Will set properly below
                    "LoggingEnabled": False,  # Will set properly below
                    "service": self.SERVICE_NAME
                }

                # Private Zone Network Config
                if visibility == 'PRIVATE' and 'privateVisibilityConfig' in zone._properties:
                    private_config = zone._properties['privateVisibilityConfig']
                    if 'networks' in private_config:
                        networks = private_config['networks']
                        info["Networks"] = [
                            get_resource_name(net.get('networkUrl', ''))
                            for net in networks if 'networkUrl' in net
                        ]

                # Forwarding Config
                if 'forwardingConfig' in zone._properties:
                    forwarding_config = zone._properties['forwardingConfig']
                    if 'targetNameServers' in forwarding_config:
                        name_servers = forwarding_config['targetNameServers']
                        info["Forwarders"] = [
                            ns.get('ipv4Address', '')
                            for ns in name_servers if 'ipv4Address' in ns
                        ]

                # DNSSEC config
                if 'dnssecConfig' in zone._properties:
                    dnssec_config = zone._properties['dnssecConfig']
                    info["DnssecEnabled"] = dnssec_config.get('state') == 'on'

                # Logging config
                if 'cloudLoggingConfig' in zone._properties:
                    logging_config = zone._properties['cloudLoggingConfig']
                    info["LoggingEnabled"] = logging_config.get('enableLogging', False)

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing DNS zones or accessing DNS API. Ensure API is enabled and necessary roles granted (e.g., 'DNS Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled or project has no DNS zones
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud DNS API might not be enabled or project not found / no zones exist. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process DNS Managed Zones: {e}", exc_info=True)
            return [] # Fail gracefully

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud DNS Managed Zone fetch. Found {len(inventory)} zones.")
        return inventory