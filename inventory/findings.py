import os
import sys
import time
import json
import re
import logging
import threading
from typing import List, Dict, Any, Optional, Tuple
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor, as_completed

from google.auth import default
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("gcp_findings")

class GCPFindingsGenerator:
    def __init__(self, max_workers: int = 20, locations: Optional[List[str]] = None):
        self.credentials = None
        self.max_workers = max_workers
        self.thread_local = threading.local()
        self.authenticate()
        self._locations = locations or self.get_default_locations()


    def authenticate(self):
        try:
            self.credentials, _ = default(
                scopes=[
                    'https://www.googleapis.com/auth/cloud-platform',
                    'https://www.googleapis.com/auth/recommender'
                ]
            )
            logger.info("Successfully authenticated with Google Cloud")
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            raise

    def get_thread_services(self) -> Tuple[Any, Any]:
        if not hasattr(self.thread_local, 'recommender_service'):
            self.thread_local.recommender_service = build(
                'recommender', 'v1',
                credentials=self.credentials,
                cache_discovery=False
            )
            self.thread_local.resource_manager_service = build(
                'cloudresourcemanager', 'v1',
                credentials=self.credentials,
                cache_discovery=False
            )
        return (self.thread_local.recommender_service,
                self.thread_local.resource_manager_service)

    def get_projects(self) -> List[str]:
        try:
            _, resource_manager_service = self.get_thread_services()
            request = resource_manager_service.projects().list()
            projects = []
            while request is not None:
                response = request.execute()
                for project in response.get('projects', []):
                    if project.get('lifecycleState') == 'ACTIVE':
                        projects.append(project['projectId'])
                request = resource_manager_service.projects().list_next(
                    previous_request=request, previous_response=response
                )
            logger.info(f"Found {len(projects)} active projects")
            return projects
        except HttpError as e:
            logger.error(f"Error fetching projects: {e}")
            return []

    def get_default_locations(self) -> List[str]:
        # Focus on commonly-used locations to improve speed
        return [
            'global',
            'us-central1',
            'us-east1',
            'us-west1',
            'europe-west1',
            'europe-west4',
            'asia-south1',
            'asia-northeast1',
        ]

    @lru_cache(maxsize=None)
    def get_recommender_types(self) -> List[Dict[str, str]]:
        # Focus set based on your payloads: IAM, Compute images/disks, Cloud Run, GKE, and a few core categories
        return [
            # Security / IAM
            {'id': 'google.iam.policy.Recommender', 'category': 'Security', 'resource_type': 'IAM Policy'},
            {'id': 'google.iam.policy.ChangeRiskRecommender', 'category': 'Reliability', 'resource_type': 'IAM Policy (Change Risk)'},
            {'id': 'google.resourcemanager.project.ChangeRiskRecommender', 'category': 'Reliability', 'resource_type': 'Project (Change Risk)'},

            # Compute cost: images and disks idle/zombie
            {'id': 'google.compute.image.IdleResourceRecommender', 'category': 'Cost', 'resource_type': 'Compute Image'},
            {'id': 'google.compute.disk.IdleResourceRecommender', 'category': 'Cost', 'resource_type': 'Compute Disk'},

            # Cloud Run
            {'id': 'google.run.service.IdentityRecommender', 'category': 'Security', 'resource_type': 'Cloud Run Service'},

            # GKE
            {'id': 'google.container.DiagnosisRecommender', 'category': 'Reliability', 'resource_type': 'GKE Cluster'},
            {'id': 'google.kubernetes.cluster.EnableMultiZonalNodePoolsRecommender', 'category': 'Reliability', 'resource_type': 'GKE Cluster'},
            {'id': 'google.kubernetes.cluster.EnableRegionalControlPlaneRecommender', 'category': 'Reliability', 'resource_type': 'GKE Cluster'},
        ]

    # ---------- Normalization helpers tuned to your payloads ----------

    def summarize_operations(self, content: Dict[str, Any]) -> str:
        groups = content.get('operationGroups', [])
        ops_text = []
        for g in groups:
            for op in g.get('operations', []):
                action = op.get('action')
                path = op.get('path') or ''
                res = op.get('resource', '')
                res_tail = res.split('/')[-1] if res else ''
                if action == 'replace' and path:
                    ops_text.append(f"{res_tail}: set {path}")
                elif action == 'add' and path:
                    ops_text.append(f"{res_tail}: add {path}")
                elif action == 'remove' and path:
                    ops_text.append(f"{res_tail}: remove {path}")
                elif action:
                    ops_text.append(f"{res_tail}: {action}")
        return '; '.join(ops_text)[:2000]

    def map_action(self, rec: Dict[str, Any], recommender_id: str) -> str:
        subtype = (rec.get('recommenderSubtype') or '').lower()
        rid = recommender_id.lower()

        if 'compute.googleapis.com/image' in json.dumps(rec).lower():
            return 'Delete unused custom image'
        if 'snapshot and delete' in json.dumps(rec).lower():
            return 'Snapshot disk and remove unused disk'
        if 'iam.policy' in rid and ('remove' in json.dumps(rec).lower() or '/iamPolicy/bindings' in json.dumps(rec)):
            return 'Remove excessive IAM binding'
        if 'run.googleapis.com/service' in json.dumps(rec).lower():
            return 'Redeploy with dedicated service account'
        if 'admissionwebhookrecommendation' in json.dumps(rec).lower():
            return 'Fix broken admission webhooks'
        if 'machine' in subtype or 'rightsize' in rid:
            return 'Right-size resource'
        if 'highavailability' in rid:
            return 'Enable high availability'
        if 'security' in rid:
            return 'Tighten security configuration'
        return 'Apply recommended change'

    def map_rationale(self, rec: Dict[str, Any], recommender_id: str) -> str:
        impact = rec.get('primaryImpact', {}).get('category', '')
        if impact == 'COST':
            return 'Reduce cost'
        if impact == 'SECURITY':
            return 'Reduce security risk by least privilege'
        if impact == 'PERFORMANCE':
            return 'Improve performance'
        if impact == 'RELIABILITY':
            return 'Improve reliability'
        # Fallback by pattern
        text = json.dumps(rec).lower()
        if 'snapshot and delete' in text or 'image' in text:
            return 'Reduce storage cost'
        if 'admissionwebhookrecommendation' in text:
            return 'Improve cluster reliability'
        if 'run.googleapis.com/service' in text:
            return 'Follow least-privilege for workloads'
        return 'General improvement'

    def estimate_effort(self, rec: Dict[str, Any]) -> str:
        ops = sum(len(g.get('operations', [])) for g in rec.get('content', {}).get('operationGroups', []))
        if ops <= 1:
            return 'Low'
        if ops <= 4:
            return 'Medium'
        return 'High'

    # Pattern parsers

    def parse_iam_change(self, content: Dict[str, Any]) -> Dict[str, str]:
        ov = content.get('overview', {})
        member = ov.get('member')
        removed_role = ov.get('removedRole') or ''
        resource = ov.get('resource') or ''
        asset = ov.get('asset', {})
        asset_name = asset.get('name') or ''
        project = asset_name or (resource.split('/')[-1] if resource else '')
        actor_type = ''
        if member:
            if member.startswith('user:'):
                actor_type = 'User'
            elif member.startswith('serviceAccount:'):
                actor_type = 'Service Account'
            elif member.startswith('group:'):
                actor_type = 'Group'
            else:
                actor_type = 'Principal'
        return {
            'iam_member': member or '',
            'iam_role': removed_role,
            'iam_actor_type': actor_type,
            'iam_project': project
        }

    def parse_compute_image(self, content: Dict[str, Any]) -> Dict[str, str]:
        ov = content.get('overview', {})
        uri = ov.get('resource') or ''
        name = ov.get('resourceName') or (uri.split('/')[-1] if uri else '')
        loc = ov.get('location') or 'global'
        return {
            'resource_id': name,
            'self_link': uri,
            'location_hint': loc,
            'action_hint': ov.get('recommendedAction', 'Delete')
        }

    def parse_compute_disk_snapshot_delete(self, content: Dict[str, Any]) -> Dict[str, str]:
        ov = content.get('overview', {})
        uri = ov.get('resource') or ''
        name = ov.get('resourceName') or (uri.split('/')[-1] if uri else '')
        loc = ov.get('location') or ''
        if not loc and 'zones/' in uri:
            loc = uri.split('zones/')[1].split('/')[0]
        ops = []
        for g in content.get('operationGroups', []):
            for op in g.get('operations', []):
                res_tail = op.get('resource', '').split('/')[-1]
                if op.get('action') == 'add' and 'Snapshot' in op.get('resourceType', ''):
                    v = op.get('value', {}) or {}
                    src = v.get('source_disk', '')
                    ops.append(f"Snapshot {res_tail} from {src}")
                elif op.get('action') == 'remove' and 'Disk' in op.get('resourceType', ''):
                    ops.append(f"Delete disk {res_tail}")
        return {
            'resource_id': name,
            'self_link': uri,
            'location_hint': loc,
            'ops_summary': '; '.join(ops) or 'Snapshot and delete'
        }

    def parse_gke_target_cluster(self, content: Dict[str, Any]) -> Dict[str, str]:
        ov = content.get('overview', {})
        t = ov.get('targetClusters', [])
        if t:
            uri = t[0].get('clusterUri', '')
            cluster_name = uri.split('/')[-1] if uri else ''
            loc = ''
            if 'locations/' in uri:
                loc = uri.split('locations/')[1].split('/')[0]
            return {
                'resource_id': cluster_name,
                'self_link': uri,
                'location_hint': loc
            }
        # operation-based
        for g in content.get('operationGroups', []):
            for op in g.get('operations', []):
                uri = op.get('resource', '')
                if 'container.googleapis.com/projects' in uri:
                    cluster_name = uri.split('/')[-1]
                    loc = ''
                    if 'locations/' in uri:
                        loc = uri.split('locations/')[1].split('/')[0]
                    return {'resource_id': cluster_name, 'self_link': uri, 'location_hint': loc}
        return {'resource_id': '', 'self_link': '', 'location_hint': ''}

    def parse_cloud_run_sa(self, content: Dict[str, Any]) -> Dict[str, str]:
        ov = content.get('overview', {})
        name = ov.get('serviceName', '')
        region = ov.get('serviceRegion', '')
        uri = ''
        for g in content.get('operationGroups', []):
            for op in g.get('operations', []):
                if 'run.googleapis.com/Service' in op.get('resourceType', ''):
                    uri = op.get('resource', '')
                    if not name:
                        name = uri.split('/')[-1]
        return {
            'resource_id': name,
            'self_link': uri,
            'location_hint': region,
            'action_hint': 'Configure service account and redeploy'
        }

    # ---------- Core parsing ----------

    def extract_resource_info(self, content: Dict[str, Any], recommender_type: str) -> Dict[str, str]:
        resource_info = {'resource_name': 'Unknown', 'resource_id': 'Unknown', 'additional_info': ''}

        try:
            # Compute: Image delete
            has_compute_image_remove = any(
                op.get('action') == 'remove' and op.get('resourceType', '') == 'compute.googleapis.com/Image'
                for g in content.get('operationGroups', []) for op in g.get('operations', [])
            )
            if has_compute_image_remove:
                p = self.parse_compute_image(content)
                return {
                    'resource_name': p['self_link'] or p['resource_id'],
                    'resource_id': p['resource_id'],
                    'additional_info': f"Action: {p['action_hint']}, Location: {p['location_hint']}"
                }

            # Compute: Disk snapshot + delete
            has_snapshot_add = any(
                'compute.googleapis.com/Snapshot' in op.get('resourceType', '')
                for g in content.get('operationGroups', []) for op in g.get('operations', [])
            )
            has_disk_remove = any(
                'compute.googleapis.com/Disk' in op.get('resourceType', '') and op.get('action') == 'remove'
                for g in content.get('operationGroups', []) for op in g.get('operations', [])
            )
            if has_snapshot_add and has_disk_remove:
                p = self.parse_compute_disk_snapshot_delete(content)
                return {
                    'resource_name': p['self_link'] or p['resource_id'],
                    'resource_id': p['resource_id'],
                    'additional_info': p['ops_summary']
                }

            # IAM change (project-level)
            is_iam = 'iamPolicy' in json.dumps(content.get('operationGroups', [])) or \
                     'removedRole' in json.dumps(content.get('overview', {}))
            if is_iam and 'google.iam.policy' in recommender_type:
                p = self.parse_iam_change(content)
                rn = content.get('overview', {}).get('resource') or content.get('overview', {}).get('asset', {}).get('name', '')
                rn = rn or p.get('iam_project', '')
                addl = f"Member: {p.get('iam_member','')}, Role: {p.get('iam_role','')}, Type: {p.get('iam_actor_type','')}"
                return {
                    'resource_name': rn,
                    'resource_id': p.get('iam_project','') or rn.split('/')[-1] or 'project',
                    'additional_info': addl
                }

            # GKE cluster + webhook
            has_gke = ('targetClusters' in content.get('overview', {})) or \
                      any('container.googleapis.com/Cluster' in op.get('resourceType', '')
                          for g in content.get('operationGroups', []) for op in g.get('operations', []))
            if has_gke or 'google.container.DiagnosisRecommender' in recommender_type:
                p = self.parse_gke_target_cluster(content)
                addl = []
                whs = content.get('overview', {}).get('admissionWebhookRecommendation', [])
                if whs:
                    first = whs[0]
                    addl.append(f"Webhook: {first.get('webhookName','')}, Type: {first.get('webhookRecommendationType','')}")
                return {
                    'resource_name': p.get('self_link') or p.get('resource_id') or 'Unknown',
                    'resource_id': p.get('resource_id') or 'Unknown',
                    'additional_info': '; '.join(addl)
                }

            # Cloud Run service SA
            has_run = any('run.googleapis.com/Service' in op.get('resourceType', '')
                          for g in content.get('operationGroups', []) for op in g.get('operations', []))
            if has_run:
                p = self.parse_cloud_run_sa(content)
                return {
                    'resource_name': p['self_link'] or p['resource_id'],
                    'resource_id': p['resource_id'] or 'Unknown',
                    'additional_info': f"{p.get('action_hint','')}, Region: {p.get('location_hint','')}"
                }

            # Generic fallback using operationGroups
            for g in content.get('operationGroups', []):
                for op in g.get('operations', []):
                    resource = op.get('resource', '')
                    if resource:
                        rid = resource.split('/')[-1]
                        res_parts = resource.split('/')
                        rtype = res_parts[-2] if len(res_parts) >= 2 else ''
                        return {
                            'resource_name': resource,
                            'resource_id': rid,
                            'additional_info': f"Type: {rtype}"
                        }

            # Legacy fallback: overview.resourceName
            ov = content.get('overview', {})
            rn = ov.get('resourceName') or ov.get('resource') or ''
            if rn:
                return {
                    'resource_name': rn,
                    'resource_id': rn.split('/')[-1],
                    'additional_info': ''
                }

        except Exception as e:
            logger.debug(f"Error parsing resource info: {e}")

        return resource_info

    # ---------- Recommender data collection ----------

    def get_recommendations_for_project_and_location(self, project_id: str, location: str, recommender_type: Dict[str, str]) -> List[Dict[str, Any]]:
        try:
            recommender_service, _ = self.get_thread_services()
            parent = f"projects/{project_id}/locations/{location}/recommenders/{recommender_type['id']}"
            request = recommender_service.projects().locations().recommenders().recommendations().list(
                parent=parent, pageSize=1000
            )

            recommendations = []
            while request is not None:
                try:
                    response = request.execute()
                    for recommendation in response.get('recommendations', []):
                        content = recommendation.get('content', {}) or {}
                        resource_info = self.extract_resource_info(content, recommender_type['id'])

                        # IAM specific fields
                        iam = {}
                        try:
                            ops = content.get('operationGroups', [])
                            if ops and 'iamPolicy' in json.dumps(ops).lower():
                                iam = self.parse_iam_change(content)
                        except Exception:
                            iam = {}

                        # Build normalized record
                        rec = {
                            'project_id': project_id,
                            'location': location,
                            'resource_type': recommender_type['resource_type'],
                            'category': recommender_type['category'],
                            'description': recommendation.get('description', ''),
                            'resource': resource_info['resource_id'],
                            'resource_name': resource_info['resource_name'],
                            'recommender_subtype': recommendation.get('recommenderSubtype'),
                            'additional_info': resource_info['additional_info'],
                            'priority': recommendation.get('priority', 'MEDIUM'),
                            'impact': recommendation.get('primaryImpact', {}).get('category', 'UNKNOWN'),
                            'state': recommendation.get('stateInfo', {}).get('state', 'ACTIVE'),
                            'recommender_id': recommender_type['id'],
                        }

                        # Add normalized columns
                        rec['action'] = self.map_action(recommendation, recommender_type['id'])
                        rec['rationale'] = self.map_rationale(recommendation, recommender_type['id'])
                        rec['ops_summary'] = self.summarize_operations(content)
                        rec['effort'] = self.estimate_effort(recommendation)
                        rec['related_member'] = iam.get('iam_member', '')
                        rec['related_role'] = iam.get('iam_role', '')
                        rec['related_principal_type'] = iam.get('iam_actor_type', '')

                        recommendations.append(rec)

                    request = recommender_service.projects().locations().recommenders().recommendations().list_next(
                        previous_request=request, previous_response=response
                    )
                except HttpError as e:
                    if e.resp.status == 403:
                        break
                    else:
                        logger.warning(f"Error fetching recommendations for {project_id} at {location}: {e}")
                        break

            return recommendations
        except Exception as e:
            logger.debug(f"Error getting recommendations for {project_id} in {location}: {e}")
            return []

    def get_recommendations_for_project(self, project_id: str, recommender_type: Dict[str, str]) -> List[Dict[str, Any]]:
        locations = self._locations
        all_recommendations = []
        # limit per-project location concurrency to avoid 403/latency bursts
        with ThreadPoolExecutor(max_workers=min(len(locations), 8)) as executor:
            future_to_location = {
                executor.submit(self.get_recommendations_for_project_and_location, project_id, location, recommender_type): location
                for location in locations
            }
            for future in as_completed(future_to_location):
                try:
                    recs = future.result(timeout=45)
                    all_recommendations.extend(recs)
                except Exception as e:
                    logger.debug(f"Location error for {project_id}: {e}")
        return all_recommendations

    def process_project_recommender_pair(self, args) -> tuple:
        project_id, recommender_type = args
        start_time = time.time()
        recommendations = self.get_recommendations_for_project(project_id, recommender_type)
        duration = time.time() - start_time
        locations_found = set(rec['location'] for rec in recommendations) if recommendations else set()
        return project_id, recommender_type['id'], recommendations, locations_found, duration

    def collect_all_findings(self, project_ids: List[str]) -> List[Dict[str, Any]]:
        all_findings = []
        recommender_types = self.get_recommender_types()
        tasks = []
        for project_id in project_ids:
            for recommender_type in recommender_types:
                tasks.append((project_id, recommender_type))

        logger.info(f"Processing {len(tasks)} tasks ({len(project_ids)} projects × {len(recommender_types)} recommenders)")
        logger.info(f"Using {self.max_workers} parallel workers")

        completed = 0
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_task = {executor.submit(self.process_project_recommender_pair, t): t for t in tasks}
            for future in as_completed(future_to_task):
                project_id, rec_id = future_to_task[future]
                try:
                    project_id, recommender_id, recs, locations_found, duration = future.result(timeout=180)
                    all_findings.extend(recs)
                    completed += 1
                    if recs:
                        logger.info(f"[{completed}/{len(tasks)}] {project_id} | {recommender_id} | "
                                    f"Found {len(recs)} in {len(locations_found)} locations ({duration:.1f}s)")
                    if completed % max(1, len(tasks) // 10) == 0:
                        elapsed = time.time() - start_time
                        progress = (completed / len(tasks)) * 100
                        eta = (elapsed / completed) * (len(tasks) - completed) if completed else 0
                        logger.info(f"Progress: {progress:.1f}% | ETA: {eta:.0f}s | Findings so far: {len(all_findings)}")
                except Exception as e:
                    completed += 1
                    logger.error(f"Error task {project_id} | {rec_id}: {e}")

        total = time.time() - start_time
        logger.info(f"Completed all tasks in {total:.1f}s | Total findings: {len(all_findings)}")
        return all_findings

    # ---------- Excel ----------

    def create_excel(self, client_findings: List[Dict[str, Any]], change_risk_findings: List[Dict[str, Any]], filename: str = "gcp_findings.xlsx") -> str:
        wb = openpyxl.Workbook()

        def write_sheet(sheet_name: str, findings: List[Dict[str, Any]]):
            ws = wb.create_sheet(title=sheet_name)
            headers = [
                "Project ID","Location","Resource Type","Impact","Description",
                "Resource","Recommender Subtype","Selflink Resource Name","Additional Info",
                "Priority","State","Recommender ID",
                "Action","Rationale","Ops Summary","Effort",
                "Related Member","Related Role","Related Principal Type"
            ]
            # Header style
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            ws.append(headers)
            for col in range(1, len(headers)+1):
                cell = ws.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment

            # Rows
            for f in findings:
                ws.append([
                    f.get('project_id',''),
                    f.get('location',''),
                    f.get('resource_type',''),
                    f.get('impact',''),
                    f.get('description',''),
                    f.get('resource',''),
                    f.get('recommender_subtype',''),
                    f.get('resource_name',''),
                    f.get('additional_info',''),
                    f.get('priority',''),
                    f.get('state',''),
                    f.get('recommender_id',''),
                    f.get('action',''),
                    f.get('rationale',''),
                    f.get('ops_summary',''),
                    f.get('effort',''),
                    f.get('related_member',''),
                    f.get('related_role',''),
                    f.get('related_principal_type',''),
                ])

            # Auto width and formatting
            ws.auto_filter.ref = ws.dimensions
            ws.freeze_panes = "A2"
            wrap_cols = [5, 9, 15, 16]  # Description, Additional Info, Action, Ops Summary
            max_widths = {i: 0 for i in range(1, len(headers)+1)}
            for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=len(headers)):
                for cell in row:
                    text = str(cell.value) if cell.value is not None else ''
                    max_widths[cell.column] = max(max_widths[cell.column], min(len(text), 100))
            for col_idx in range(1, len(headers)+1):
                width = min(max_widths[col_idx] + 2, 80 if col_idx in wrap_cols else 50)
                ws.column_dimensions[get_column_letter(col_idx)].width = width
            for col in wrap_cols:
                # Apply wrap alignment to the whole column
                for row_idx in range(2, ws.max_row + 1):
                    ws.cell(row=row_idx, column=col).alignment = Alignment(wrap_text=True, vertical="top")

        # Remove the default sheet and create Findings
        default = wb.active
        wb.remove(default)
        write_sheet("Findings", client_findings)
        write_sheet("Change Risk", change_risk_findings)

        wb.save(filename)
        logger.info(f"Excel file saved as: {filename}")
        return filename
# ---------- CLI ----------
def parse_args(argv: List[str]) -> Tuple[int, List[str], Optional[List[str]]]:
    max_workers = 20
    project_ids: List[str] = []
    locations: Optional[List[str]] = None


    i = 1
    while i < len(argv):
        if argv[i] == '--workers' and i + 1 < len(argv):
            max_workers = int(argv[i+1]); i += 2
        elif argv[i] == '--locations' and i + 1 < len(argv):
            loc = argv[i+1].strip()
            locations = [x.strip() for x in loc.split(',') if x.strip()]
            i += 2
        else:
            project_ids.append(argv[i]); i += 1
    return max_workers, project_ids, locations

def main():
    try:
        max_workers, project_ids, locations = parse_args(sys.argv)
        if not project_ids:
            print("Usage: python gcp_findings_client_grade.py [--workers N] [--locations 'global,us-central1,europe-west1'] PROJECT_ID [PROJECT_ID2 ...]")
            print("Example: python gcp_findings_client_grade.py --workers 20 --locations 'global,europe-west1,asia-south1' ce-ps3")
            return

        print(f"Initializing with {max_workers} parallel workers...")
        gen = GCPFindingsGenerator(max_workers=max_workers, locations=locations)

        print(f"Scanning projects: {project_ids}")
        start = time.time()
        findings = gen.collect_all_findings(project_ids)
        scan_time = time.time() - start

        # Split Change Risk and client-facing
        change_risk = [f for f in findings if "(Change Risk)" in f.get('resource_type','') or 'changeriskrecommender' in f.get('recommender_id','').lower()]
        client_findings = [f for f in findings if f not in change_risk]

        print("Creating Excel sheet...")
        filename = gen.create_excel(client_findings, change_risk, filename="gcp_findings.xlsx")
        total = time.time() - start

        print("\n" + "="*60)
        print(f"GCP Findings sheet created successfully: {filename}")
        print(f"Total findings: {len(client_findings)} (Change Risk: {len(change_risk)})")
        print(f"Scan time: {scan_time:.1f}s | Total time: {total:.1f}s")
        if scan_time > 0:
            print(f"Average speed: {len(findings)/scan_time:.1f} findings/second")

        # Quick summaries
        categories = {}
        locations_count = {}
        for f in client_findings:
            categories[f.get('category','Unknown')] = categories.get(f.get('category','Unknown'), 0) + 1
            locations_count[f.get('location','Unknown')] = locations_count.get(f.get('location','Unknown'), 0) + 1

        print("\nFindings by category:")
        for k in sorted(categories.keys()):
            print(f"  {k}: {categories[k]}")

        print("\nTop 10 locations by findings:")
        for loc, cnt in sorted(locations_count.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"  {loc}: {cnt}")

    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
