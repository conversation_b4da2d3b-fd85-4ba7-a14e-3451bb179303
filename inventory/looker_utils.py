import urllib.parse
import sys
from typing import Dict, <PERSON><PERSON>

try:
    from googleapiclient.discovery import build
    from google.auth import default
    from google.auth.exceptions import DefaultCredentialsError
    GOOGLE_APIS_AVAILABLE = True
except ImportError:
    GOOGLE_APIS_AVAILABLE = False
gcp_looker_dashboard_template = "b0910402-2617-430e-a80c-40e6d0135713"
aws_looker_dashboard_template = "6c07d885-ce5d-4b3b-9766-44e42aaae055"
report_name = "Inventory Dashboard"

datasource_map_gcp = {
    "ds0": "compute",
    "ds1": "cloud_sql",
    "ds2": "cloud_run_service",
    "ds3": "gke_cluster",
    "ds4": "vpc_network",
    "ds5": "firewall_rule",
    "ds6": "Firewall-Policy-Rules",
    "ds7": "subnet",
    "ds8": "Quotas"
}
datasource_map_aws = {
    "ds1": "EC2",
    "ds2": "RDS",
    "ds3": "S3",
    "ds4": "LAMBDA",
    "ds5": "ROUTE53",
    "ds6": "CLOUDFRONT",
    "ds7": "ECS"
}


def _extract_spreadsheet_id(sheets_url: str) -> str:
    """Extract spreadsheet ID from Google Sheets URL."""
    if "/spreadsheets/d/" in sheets_url:
        start = sheets_url.find("/spreadsheets/d/") + len("/spreadsheets/d/")
        end = sheets_url.find("/", start)
        if end == -1:
            end = len(sheets_url)
        return sheets_url[start:end]
    else:
        raise ValueError("Invalid Google Sheets URL format.")

def get_worksheet_info(spreadsheet_id: str) -> Tuple[str, Dict[str, str]]:
    """
    Get the spreadsheet title and a dictionary of its worksheet names and IDs.
    """
    if not GOOGLE_APIS_AVAILABLE:
        raise ImportError("Google API client libraries are not installed. Please run: pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")

    try:
        credentials, _ = default()
        sheets_service = build('sheets', 'v4', credentials=credentials)
    except DefaultCredentialsError:
        raise ConnectionError("Google Application Default Credentials are not set up. Please authenticate by running: gcloud auth application-default login")
    except Exception as e:
        raise ConnectionError(f"An unexpected error occurred during authentication: {e}")

    try:
        spreadsheet = sheets_service.spreadsheets().get(
            spreadsheetId=spreadsheet_id
        ).execute()

        properties = spreadsheet.get('properties', {})
        spreadsheet_title = properties.get('title', 'Untitled Spreadsheet')

        worksheets = {}
        for sheet in spreadsheet.get('sheets', []):
            sheet_properties = sheet.get('properties', {})
            name = sheet_properties.get('title', 'Unnamed Sheet')
            sheet_id = str(sheet_properties.get('sheetId', 0))
            worksheets[name] = sheet_id

        return spreadsheet_title, worksheets
    except Exception as e:
        raise ConnectionError(f"An error occurred while fetching sheet data: {e}")

def generate_looker_studio_url(gcp_looker_dashboard_template: str, report_name: str, spreadsheet_id: str, datasource_map: Dict[str, str]) -> str:
    """
    Generates a Looker Studio URL for creating a new report from a template.
    """
    spreadsheet_title, worksheets = get_worksheet_info(spreadsheet_id)

    base_url = "https://lookerstudio.google.com/reporting/create"
    params = {
        "c.reportId": gcp_looker_dashboard_template,
        "c.mode": "edit",
        "r.reportName": report_name
    }

    for ds_alias, worksheet_name in datasource_map.items():
        if worksheet_name in worksheets:
            worksheet_id = worksheets[worksheet_name]
            
            ds_name = f"{spreadsheet_title} - {worksheet_name}"
            
            prefix = f"ds.{ds_alias}"
            params[f"{prefix}.connector"] = "googleSheets"
            params[f"{prefix}.datasourceName"] = ds_name
            params[f"{prefix}.spreadsheetId"] = spreadsheet_id
            params[f"{prefix}.worksheetId"] = worksheet_id
            params[f"{prefix}.hasHeader"] = "true"
            params[f"{prefix}.includeHiddenCells"] = "true"
            params[f"{prefix}.includeFilteredCells"] = "true"
            params[f"{prefix}.refreshFields"] = "true"
        else:
            print(f"Warning: Worksheet '{worksheet_name}' not found in the Google Sheet. Skipping data source '{ds_alias}'.")

    query_string = urllib.parse.urlencode(params)
    final_url = f"{base_url}?{query_string}"
    return final_url


def main(request, rootpath):
    caller = str(request.form['caller'])
    if caller == "gcp":
        datasource_map = datasource_map_gcp
        looker_dashboard_template = gcp_looker_dashboard_template
    elif caller == "aws":
        datasource_map = datasource_map_aws
        looker_dashboard_template = aws_looker_dashboard_template

    google_sheets_url = request.form['sheet_url']
    try:
        spreadsheet_id = _extract_spreadsheet_id(google_sheets_url)
        final_url = generate_looker_studio_url(
            looker_dashboard_template,
            report_name,
            spreadsheet_id,
            datasource_map
        )
        return final_url, "", "Looker Studio URL generated successfully.", 0
    except (ValueError, ImportError, ConnectionError) as e:
        return "", f"Error generating Looker Studio URL.", "Error generating Looker Studio URL.", 1
    
# if __name__ == '__main__':
#     # --- Configuration ---
    
#     # The URL of the Google Sheet
#     google_sheets_url = "https://docs.google.com/spreadsheets/d/1RtLehY7lHfJfYwZ7suGYWfBCHM7fRs108O9qVE0N-cs/edit"
    
#     # Mapping of data source aliases to worksheet names

#     try:
#         spreadsheet_id = _extract_spreadsheet_id(google_sheets_url)
#         final_url = generate_looker_studio_url(
#             gcp_looker_dashboard_template,
#             report_name,
#             spreadsheet_id,
#             datasource_map
#         )
#         print("\n--- Generated Looker Studio URL ---")
#         print(final_url)
#     except (ValueError, ImportError, ConnectionError) as e:
#         print(f"Error: {e}")
#         sys.exit(1)
