You are assisting a cloud modernization team that has already shut down further GCP expansion and is preparing for a full exit. Your insights must cover every resource group, from simple VMs to complex PaaS services, to create a comprehensive work breakdown structure.

**OBJECTIVE:**
From the GCP inventory snapshot provided, generate a complete **resource migration manifest**. Every GCP resource or resource group identified in the inventory must be included in the output, detailing the migration path, required teams, and complexity, regardless of whether it is a simple "lift-and-shift" or a complex "re-architect" task.

---

**STRICT OUTPUT FORMAT:**
Use the following pipe-delimited format (no headers, no markdown, no additional notes):

`GCP Service | Affected Count | Challenge Category | The Challenge (Technical Details) | Business Impact / Why it Matters | AWS Recommendation (Tool & Action) | Required Teams | Complexity`

---

**RULES FOR ANALYSIS:**

1.  **Catalog All GCP Resources for Migration:**
    * You must create an entry for **every resource type** found in the inventory.
    * This includes services with direct AWS equivalents (e.g., GCE, GCS, Cloud SQL) as well as those requiring complex redesign. The goal is a complete manifest, not just a risk register.
    * For simple resources, the "Challenge" is the migration task itself.

2.  **Quantify, Group & Aggregate Challenges:**
    * Group GCP resources of the same type that share the same migration pattern.
    * Always specify the number and the correct unit (e.g., "150 Instances", "45 Buckets", "14 Cloud Deployment Manager Templates").

3.  **Analyze All GCP-Specific Constructs:**
    * Provide detailed analysis for resources that are:
        * **Heavily integrated with GCP IAM/Workload Identity**,
        * **Automation-bound (Cloud Deployment Manager, Cloud Workflows, Dataform)**,
        * **Deeply tied to GCP-native analytics (BigQuery, Dataflow, Cloud Pub/Sub)**,
        * **Built on advanced networking constructs (VPC Peering, Shared VPC, Private Service Connect)**.

4.  **Keep Names Anonymous, But Preserve Config Intelligence:**
    * Mask project IDs, resource names, regions, and IPs.
    * Retain key technical data like machine type, version, runtime, protocol, or architectural choices.

5.  **Assign Required Teams:**
    * For each challenge, specify the combination of teams required to resolve it. Choose from the following predefined list:
        * **Infra Only**: Core infrastructure tasks (networking, base compute).
        * **Infra + App lite**: Infra work with minor application configuration changes.
        * **Infra + App**: Requires deep collaboration with application development teams.
        * **Infra + Data**: Involves data engineering or database teams.
        * **Infra + Data + App**: A complex change involving all three teams.
        * **Infra + Data + App + AI**: A highly specialized change involving AI/ML teams.

6.  **Assign Complexity Level:**
    * Rate the complexity of the required engineering effort on a four-point scale:
        * **Low**: Configuration changes, direct resource migration using standard tools, simple script adaptations.
        * **Medium**: Requires new IaC, refactoring of minor components, or use of a new managed service with some adaptation.
        * **High**: Requires significant re-architecture of a component or workflow; full reimplementation of a business process.
        * **Very High**: Involves modernizing a core platform (e.g., data warehouse, orchestration engine) with significant business logic and integration changes.

---

**CHALLENGE CATEGORIES TO CHOOSE FROM:**

**Direct Resource Mapping** — A GCP service or resource has a direct, functionally equivalent counterpart in AWS, and the primary task is migration/replication rather than redesign.
**PaaS Feature Mismatch** — GCP-specific service behavior or capability that has no direct equivalent in AWS.
**Unsupported Dependency** — OS/software/runtime/configuration not supported in AWS tools like DMS, MGN, or Lambda.
**Identity & Access Federation** — Breakage in role chaining, federated SSO, or trust policies when mapping to AWS IAM.
**Automation & Infrastructure Refactoring** — GCP-native automation stacks that don’t translate (e.g., Deployment Manager, Cloud Workflows).
**Observability & Monitoring Gaps** — Deep Cloud Monitoring/Logging/Trace integrations that need rebuilding in AWS.
**Networking Paradigm Shift** — Shared VPC, Private Service Connect, or inter-VPC routing patterns that require redesign in AWS’s networking model.
**Data Platform Modernization** — Heavy usage of BigQuery, Dataflow, or Pub/Sub that requires AWS-native redesign.
**Security & Compliance Mapping** — IAM boundaries, Organization Policies, or Firewall Rules that require reinterpretation.
**API & Gateway Redesign** — Migration from Cloud Endpoints or API Gateway to Amazon API Gateway or ALB/NLB.
**Backup & DR Rebuild** — GCP-native DR setups like custom snapshot schedules, cross-region replication, or Cloud Backup requiring full redesign.

---

**EXAMPLES OF VALID OUTPUT:**

`Google Compute Engine|150 Instances|Direct Resource Mapping|Standard n1-highcpu-2 instances running CentOS 7 with a custom application stack.|These instances host core business applications and must be migrated with minimal downtime.|Use AWS Application Migration Service (MGN) for wave-based migration. Validate performance and networking post-migration.|Infra + App lite|Low`
`Google Cloud Storage|45 Buckets|Direct Resource Mapping|Standard-tier buckets used for log archival and static asset hosting. No special features like Object Versioning are in use.|Serves as a critical data store for application logs and front-end assets. Data integrity must be maintained.|Use AWS DataSync to sync data to Amazon S3. Update application configurations to point to S3 endpoints.|Infra + App lite|Low`
`Google Cloud Workflows|18 Workflows|Automation & Infrastructure Refactoring|Workflows include nested state machines with service integrations (e.g., Cloud Functions, BigQuery) that rely on GCP-specific execution semantics.|These workflows cannot be ported to AWS as-is, breaking internal automation and batch data processing pipelines.|Re-implement workflows in AWS Step Functions or a custom orchestration service. Replace integrations with Lambda + SQS or AWS Glue.|Infra + App|High`
`Google Cloud SQL|3 Clusters|PaaS Feature Mismatch|Clusters run with High Availability and cross-region replication using GCP-native features, not natively supported in Amazon RDS.|Breaks failover and cross-region replication strategies, affecting availability SLAs.|Migrate to Amazon RDS with Multi-AZ deployment, or use a custom HA solution with a separate replication strategy.|Infra + Data + App|High`
`Shared VPC|2 Host Projects|Networking Paradigm Shift|Centralized hub with routing to 14 service projects. AWS lacks Shared VPC equivalent, making this topology non-trivial to replicate.|Inter-VPC communication requires complex peerings or AWS Transit Gateway, impacting latency and routing simplicity.|Use AWS Transit Gateway and design segmented routing domains with explicit intent.|Infra Only|High`
`Google BigQuery|2 Datasets|Data Platform Modernization|Datasets rely on materialized views, UDFs in Javascript, and federated queries from GCS.|Breaks analytic pipelines and reporting dashboards. Redesign required for federated model and view management.|Use Amazon Redshift or Athena with DataBrew for orchestration. Translate UDFs and use S3 with External Tables for staged queries.|Infra + Data|Very High`

---

**INVENTORY SNAPSHOT PROVIDED BELOW:**
: {data_text}
Analyze and respond based only on that data.