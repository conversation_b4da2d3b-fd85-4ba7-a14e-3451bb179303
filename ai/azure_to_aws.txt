You are assisting a cloud modernization team that has already shut down further Azure expansion and is preparing for a full exit. Your insights must cover every resource group, from simple VMs to complex PaaS services, to create a comprehensive work breakdown structure.

**OBJECTIVE:**
From the Azure inventory snapshot provided, generate a complete **resource migration manifest**. Every Azure resource or resource group identified in the inventory must be included in the output, detailing the migration path, required teams, and complexity, regardless of whether it is a simple "lift-and-shift" or a complex "re-architect" task.

---

**STRICT OUTPUT FORMAT:**
Use the following pipe-delimited format (no headers, no markdown, no additional notes):

`Azure Service | Affected Count | Challenge Category | The Challenge (Technical Details) | Business Impact / Why it Matters | AWS Recommendation (Tool & Action) | Required Teams | Complexity`

---

**RULES FOR ANALYSIS:**

1.  **Catalog All Azure Resources for Migration:**
    * You must create an entry for **every resource type** found in the inventory.
    * This includes services with direct AWS equivalents (e.g., Azure VMs, Azure Storage, Azure SQL Database) as well as those requiring complex redesign. The goal is a complete manifest, not just a risk register.
    * For simple resources, the "Challenge" is the migration task itself.

2.  **Quantify, Group & Aggregate Challenges:**
    * Group Azure resources of the same type that share the same migration pattern.
    * Always specify the number and the correct unit (e.g., "150 Virtual Machines", "45 Storage Accounts", "14 ARM Templates").

3.  **Analyze All Azure-Specific Constructs:**
    * Provide detailed analysis for resources that are:
        * **Heavily integrated with Azure AD/Managed Identities**,
        * **Automation-bound (ARM Templates, Logic Apps, Azure Functions)**,
        * **Deeply tied to Azure-native analytics (Azure Synapse Analytics, Data Factory, Event Hubs)**,
        * **Built on advanced networking constructs (VNet Peering, Private Link, Network Security Groups)**.

4.  **Keep Names Anonymous, But Preserve Config Intelligence:**
    * Mask subscription IDs, resource names, regions, and IPs.
    * Retain key technical data like VM size, version, runtime, protocol, or architectural choices.

5.  **Assign Required Teams:**
    * For each challenge, specify the combination of teams required to resolve it. Choose from the following predefined list:
        * **Infra Only**: Core infrastructure tasks (networking, base compute).
        * **Infra + App lite**: Infra work with minor application configuration changes.
        * **Infra + App**: Requires deep collaboration with application development teams.
        * **Infra + Data**: Involves data engineering or database teams.
        * **Infra + Data + App**: A complex change involving all three teams.
        * **Infra + Data + App + AI**: A highly specialized change involving AI/ML teams.

6.  **Assign Complexity Level:**
    * Rate the complexity of the required engineering effort on a four-point scale:
        * **Low**: Configuration changes, direct resource migration using standard tools, simple script adaptations.
        * **Medium**: Requires new IaC, refactoring of minor components, or use of a new managed service with some adaptation.
        * **High**: Requires significant re-architecture of a component or workflow; full reimplementation of a business process.
        * **Very High**: Involves modernizing a core platform (e.g., data warehouse, orchestration engine) with significant business logic and integration changes.

---

**CHALLENGE CATEGORIES TO CHOOSE FROM:**

**Direct Resource Mapping** — An Azure service or resource has a direct, functionally equivalent counterpart in AWS, and the primary task is migration/replication rather than redesign.
**PaaS Feature Mismatch** — Azure-specific service behavior or capability that has no direct equivalent in AWS.
**Unsupported Dependency** — OS/software/runtime/configuration not supported in AWS tools like DMS, MGN, or Lambda.
**Identity & Access Federation** — Breakage in role chaining, federated SSO, or trust policies when mapping to AWS IAM.
**Automation & Infrastructure Refactoring** — Azure-native automation stacks that don’t translate (e.g., ARM Templates, Logic Apps).
**Observability & Monitoring Gaps** — Deep Azure Monitor/Application Insights integrations that need rebuilding in AWS.
**Networking Paradigm Shift** — VNet Peering, Private Link, or inter-VNet routing patterns that require redesign in AWS’s networking model.
**Data Platform Modernization** — Heavy usage of Azure Synapse, Data Factory, or Event Hubs that requires AWS-native redesign.
**Security & Compliance Mapping** — IAM boundaries, Azure Policy, or Network Security Group constructs that require reinterpretation.
**API & Gateway Redesign** — Migration from Azure API Management to Amazon API Gateway or ALB/NLB.
**Backup & DR Rebuild** — Azure-native DR setups like Site Recovery, snapshot orchestration, or Backup Center requiring full redesign.

---

**EXAMPLES OF VALID OUTPUT:**

`Azure Virtual Machines|150 VMs|Direct Resource Mapping|Standard D2s v3 VMs running Ubuntu 20.04 with a custom web server application.|These VMs host core business applications and must be migrated with minimal downtime.|Use AWS Application Migration Service (MGN) for wave-based migration. Validate performance and networking post-migration.|Infra + App lite|Low`
`Azure Storage Accounts|45 Accounts|Direct Resource Mapping|Standard-tier Blob storage used for log archival and static asset hosting. No special features like Object Immutability are in use.|Serves as a critical data store for application logs and front-end assets. Data integrity must be maintained.|Use AWS DataSync to sync data to Amazon S3. Update application configurations to point to S3 endpoints.|Infra + App lite|Low`
`Azure Logic Apps|18 Workflows|Automation & Infrastructure Refactoring|Workflows include nested logic with service integrations (e.g., Azure Functions, Data Factory) that rely on Azure-specific execution semantics.|These workflows cannot be ported to AWS as-is, breaking internal automation and batch data processing pipelines.|Re-implement workflows in AWS Step Functions. Replace integrations with Lambda + SQS or AWS Glue.|Infra + App|High`
`Azure SQL Database|3 Databases|PaaS Feature Mismatch|Databases use built-in Active Geo-Replication and automated failover groups, a feature not natively supported in Amazon RDS.|Breaks failover and cross-region replication strategies, affecting availability SLAs.|Migrate to Amazon RDS with Multi-AZ deployment, or use a custom HA solution with a separate replication strategy.|Infra + Data + App|High`
`Azure Virtual Network|2 VNETs|Networking Paradigm Shift|Centralized hub with VNet peering to 14 other VNets. AWS lacks a direct VNet peering equivalent, making hub-spoke topology non-trivial to replicate.|Inter-VNet communication requires complex peerings or AWS Transit Gateway, impacting latency and routing simplicity.|Use AWS Transit Gateway and design segmented routing domains with explicit intent.|Infra Only|High`
`Azure Synapse Analytics|2 Workspaces|Data Platform Modernization|Workspaces rely on serverless SQL pools, Spark notebooks, and federated queries from Azure Data Lake Storage.|Breaks analytic pipelines and reporting dashboards. Redesign required for federated model and view management.|Use Amazon Redshift or Athena with AWS Glue for orchestration. Translate notebooks and use S3 with External Tables for staged queries.|Infra + Data|Very High`

---

**INVENTORY SNAPSHOT PROVIDED BELOW:**
: {data_text}
Analyze and respond based only on that data.