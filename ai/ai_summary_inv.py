#!/usr/bin/env python3
"""
Gemini-Google Sheets Integration with multithreading for parallel resource analysis.
Reads from Google Sheets, processes with Gemini concurrently, and writes back to sheet.
"""

import os
import argparse
import logging
import concurrent.futures
from typing import Any, Dict, List, Optional, Tuple
import pandas as pd
from openpyxl import load_workbook

from google.auth import default
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google import genai

import importlib

logger = logging.getLogger(__name__)

# --- Integration Class ---
class GeminiIntegration:
    """
    Integration class for Gemini AI and Google Sheets/Excel with multithreading.
    """

    def __init__(self, config, project_id: Optional[str] = None, location: Optional[str] = None, provider: str = "GCP"):
        self.sheets_service = None
        self.gemini_client = None
        self.project_id = project_id
        self.location = location
        self.provider = provider
        self.config = config
        self.setup_gemini()

    def setup_google_sheets(self):
        if self.sheets_service:
            return
        try:
            credentials, project = default(scopes=[
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive.readonly'
            ])
            self.sheets_service = build('sheets', 'v4', credentials=credentials)
            logger.info(f"Google Sheets API initialized for project: {project}")
        except Exception as error:
            logger.error(f"Error setting up Google Sheets API: {error}")
            raise

    def setup_gemini(self):
        try:
            if not self.project_id:
                self.project_id = os.getenv('GOOGLE_CLOUD_PROJECT') or os.getenv('GCLOUD_PROJECT') or self.config.DEFAULT_PROJECT_ID
                logger.warning(f"Using default project: {self.project_id}")
            if not self.location:
                self.location = os.getenv('GOOGLE_CLOUD_LOCATION', self.config.DEFAULT_LOCATION)
            self.gemini_client = genai.Client(vertexai=True, project=self.project_id, location=self.location)
            logger.info(f"Gemini AI initialized | Project: {self.project_id} | Location: {self.location}")
        except Exception as error:
            logger.error(f"Error setting up Gemini AI: {error}")
            raise

    def gsheets_read(self, spreadsheet_id: str, ranges: Optional[List[str]] = None, sheet_id: Optional[int] = None) -> Dict[str, Any]:
        self.setup_google_sheets()
        try:
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')
            sheets = spreadsheet.get('sheets', [])
            if ranges:
                target_ranges = ranges
            else:
                if sheet_id is not None:
                    target_sheet = next((s for s in sheets if s['properties']['sheetId'] == sheet_id), None)
                    if not target_sheet:
                        raise ValueError(f"Sheet ID {sheet_id} not found")
                    sheet_name = target_sheet['properties']['title']
                    target_ranges = [sheet_name]
                else:
                    # Read all sheets if no specific range or sheet_id is given
                    target_ranges = [sheet['properties']['title'] for sheet in sheets]
                
            all_data = {}
            for range_name in target_ranges:
                result = self.sheets_service.spreadsheets().values().get(
                    spreadsheetId=spreadsheet_id,
                    range=range_name
                ).execute()
                all_data[range_name] = result.get('values', [])

            return {
                'spreadsheet_title': title,
                'ranges': target_ranges,
                'data': all_data,
                'total_rows': sum(len(v) for v in all_data.values()),
                'sheet_count': len(sheets)
            }
        except HttpError as error:
            logger.error(f"HTTP Error: {error}")
            raise
        except Exception as error:
            logger.error(f"Error fetching spreadsheet data: {error}")
            raise

    def excel_read(self, file_path: str) -> Dict[str, Any]:
        try:
            xls = pd.ExcelFile(file_path)
            all_data = {}
            for sheet_name in xls.sheet_names:
                df = pd.read_excel(xls, sheet_name=sheet_name)
                all_data[sheet_name] = [df.columns.values.tolist()] + df.values.tolist()

            return {
                'spreadsheet_title': os.path.basename(file_path),
                'ranges': list(all_data.keys()),
                'data': all_data,
                'total_rows': sum(len(v) for v in all_data.values()),
                'sheet_count': len(all_data)
            }
        except Exception as error:
            logger.error(f"Error reading Excel file: {error}")
            raise

    def create_summary_sheet(self, spreadsheet_id: str, sheet_name: str = "Analysis_Summary") -> bool:
        self.setup_google_sheets()
        try:
            request_body = {
                'requests': [{
                    'addSheet': {
                        'properties': {
                            'title': sheet_name
                        }
                    }
                }]
            }
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=request_body
            ).execute()
            logger.info(f"Created new sheet: {sheet_name}")
            return True
        except HttpError as error:
            if "already exists" in str(error):
                logger.warning(f"Sheet '{sheet_name}' already exists")
                return True
            logger.error(f"Error creating sheet: {error}")
            return False

    def format_summary_sheet_headers(self, spreadsheet_id: str, sheet_name: str = "Analysis_Summary") -> None:
        self.setup_google_sheets()
        try:
            spreadsheet = self.sheets_service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
            sheet_id = next(s['properties']['sheetId'] for s in spreadsheet['sheets'] if s['properties']['title'] == sheet_name)
            if self.provider == "GCP":
                headers = self.config.GCP_SUMMARY_HEADERS
            elif self.provider == "AZURE":
                headers = self.config.AZURE_SUMMARY_HEADERS
            elif self.provider == "AWS":
                headers = self.config.AWS_SUMMARY_HEADERS
            elif self.provider == "OCI":
                headers = self.config.OCI_SUMMARY_HEADERS
            elif self.provider == "KUBE":
                headers = self.config.KUBE_SUMMARY_HEADERS
            requests = [
                {
                    "repeatCell": {
                        "range": {"sheetId": sheet_id, "startRowIndex": 0, "endRowIndex": 1},
                        "cell": {
                            "userEnteredFormat": {
                                "backgroundColor": {"red": 0.8, "green": 0.94, "blue": 0.8},
                                "textFormat": {"bold": True},
                                "horizontalAlignment": "CENTER"
                            }
                        },
                        "fields": "userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)"
                    }
                },
                {
                    "setBasicFilter": {
                        "filter": {
                            "range": {
                                "sheetId": sheet_id,
                                "startRowIndex": 0,
                                "endRowIndex": 1,
                                "startColumnIndex": 0,
                                "endColumnIndex": len(headers)
                            }
                        }
                    }
                }
            ]
            self.sheets_service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={"requests": requests}
            ).execute()
            logger.info(f"Header formatting applied to sheet: {sheet_name}")
        except Exception as error:
            logger.error(f"Error formatting headers: {error}")

    def write_analysis_to_sheet(self, spreadsheet_id: str, analysis_text: str, sheet_name: str = "Analysis_Summary") -> bool:
        try:
            self.create_summary_sheet(spreadsheet_id, sheet_name)
            parsed_data = self.parse_pipe_delimited_response(analysis_text)
            self.sheets_service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!A:Z"
            ).execute()
            self.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=f"{sheet_name}!A1",
                valueInputOption='RAW',
                body={"values": parsed_data}
            ).execute()
            temp_dir = os.path.join(self.config.PWD, "temp")
            os.makedirs(temp_dir, exist_ok=True)
            with open(os.path.join(temp_dir, "response.txt"), "w") as f:
                f.write(analysis_text)
            logger.info("Response written and saved. Now formatting headers...")
            self.format_summary_sheet_headers(spreadsheet_id, sheet_name)
            return True
        except Exception as error:
            logger.error(f"Error writing analysis to sheet: {error}")
            return False

    def parse_pipe_delimited_response(self, response_text: str) -> List[List[str]]:
        lines = response_text.strip().split('\n')
        parsed_data = []
        if self.provider == "GCP":
            headers = self.config.GCP_SUMMARY_HEADERS
        elif self.provider == "AZURE":
            headers = self.config.AZURE_SUMMARY_HEADERS
        elif self.provider == "AWS":
            headers = self.config.AWS_SUMMARY_HEADERS
        elif self.provider == "OCI":
            headers = self.config.OCI_SUMMARY_HEADERS
        elif self.provider == "KUBE":
            headers = self.config.KUBE_SUMMARY_HEADERS
        for line in lines:
            if '|' in line:
                row = [c.strip() for c in line.split('|')]
                # Clean up empty strings that can result from leading/trailing pipes
                row = [item for item in row if item]
                if len(row) == len(headers):
                    parsed_data.append(row)
        return parsed_data

def parse_findings_response(response_text: str) -> List[List[str]]:
    """
    Parses the specific pipe-delimited response from the findings expansion prompt,
    handling and cleaning up multiple headers from merged AI responses.
    """
    lines = response_text.strip().split('\n')
    parsed_data = []
    headers = ["Project ID", "Resource", "Resource Type", "Category", "Action", "Description", "Priority"]
    
    # Add the header only once at the beginning
    parsed_data.append(headers)
    
    for line in lines:
        # Skip empty lines, markdown table separators, and repeated headers
        if not line.strip() or '---' in line:
            continue

        if '|' in line:
            row = [c.strip() for c in line.split('|')]
            # Clean up empty strings that can result from leading/trailing pipes
            row = [item for item in row if item]

            # Skip any row that is identical to the header
            if row == headers:
                continue

            if len(row) == len(headers):
                parsed_data.append(row)
    
    # Sort the data by "Resource Type" and then by "Project ID"
    if len(parsed_data) > 1:
        df = pd.DataFrame(parsed_data[1:], columns=parsed_data[0])
        df_sorted = df.sort_values(by=["Resource Type", "Project ID"])
        parsed_data = [df_sorted.columns.tolist()] + df_sorted.values.tolist()

    return parsed_data

def group_data_by_resource(data: Dict[str, Any]) -> Dict[str, List[List[str]]]:
    """Groups sheet data by resource type, assuming resource type is in the first column."""
    grouped_data = {}
    for sheet_name, values in data['data'].items():
        if not values:
            continue
        
        header = values[0]
        resource_type_from_sheet_name = sheet_name
        
        if resource_type_from_sheet_name not in grouped_data:
            grouped_data[resource_type_from_sheet_name] = [header]
        
        grouped_data[resource_type_from_sheet_name].extend(values[1:])

    return grouped_data

def format_resource_data_for_analysis(resource_name: str, rows: List[List[str]]) -> str:
    """Formats a single resource's data for Gemini analysis."""
    lines = [f"Data for resource: {resource_name}"]
    for i, row in enumerate(rows):
        row_str = [str(cell) if cell is not None else '' for cell in row]
        lines.append(f"Row {i+1}: {', '.join(row_str)}")
    return '\n'.join(lines)

def query_single_resource_with_gemini(client: genai.Client, provider: str, resource_name: str, data_text: str, config) -> Tuple[str, int, int]:
    """Query a single resource's data using Gemini AI and return the response and token count."""
    logger.info(f"Analyzing resource: {resource_name}...")
    prompt_template = getattr(config, f"{provider}_GEMINI_PROMPT")
    prompt = prompt_template.format(data_text=data_text)
    try:
        response = client.models.generate_content(model=config.GEMINI_MODEL, contents=prompt, config={"temperature":0.2})
        input_tokens = response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else 0
        output_tokens = response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') else 0
        logger.info(f"Analysis complete for: {resource_name}")
        return response.text, input_tokens, output_tokens
    except Exception as error:
        logger.error(f"Gemini API error for resource {resource_name}: {error}")
        return "", 0, 0

def query_findings_expansion_with_gemini(client: genai.Client, analysis_summary: str, inventory_snapshot: str, config) -> Tuple[str, int, int]:
    """
    Queries Gemini with the findings expansion prompt, analysis summary, and inventory data.
    """
    logger.info("Analyzing inventory to expand findings...")
    try:
        # Load the prompt from the file
        prompt_file_path = os.path.join(os.path.dirname(__file__), 'findings_expansion_prompt.txt')
        with open(prompt_file_path, 'r') as f:
            prompt_template = f.read()

        # Format the prompt with the analysis summary and inventory data
        prompt = prompt_template.format(
            analysis_summary=analysis_summary,
            inventory_snapshot=inventory_snapshot
        )
        
        response = client.models.generate_content(model=config.GEMINI_MODEL, contents=prompt, config={"temperature":0})
        input_tokens = response.usage_metadata.prompt_token_count if hasattr(response, 'usage_metadata') else 0
        output_tokens = response.usage_metadata.candidates_token_count if hasattr(response, 'usage_metadata') else 0
        logger.info("Findings expansion analysis complete.")
        return response.text, input_tokens, output_tokens
    except Exception as error:
        logger.error(f"Gemini API error during findings expansion: {error}")
        return "", 0, 0

def process_single_resource_group(client: genai.Client, provider: str, resource_name: str, rows: List[List[str]], config) -> Dict[str, Any]:
    """
    Processes a single resource group:
    1. Generates the high-level analysis summary.
    2. Expands the summary into detailed, per-resource findings.
    Returns a dictionary with results and token counts.
    """
    # Step 1: Generate high-level analysis
    data_text = format_resource_data_for_analysis(resource_name, rows)
    analysis_text, analysis_in_tokens, analysis_out_tokens = query_single_resource_with_gemini(
        client, provider, resource_name, data_text, config
    )

    if not analysis_text:
        return {"analysis_text": "", "findings_text": "", "tokens": (0, 0, 0, 0)}

    # Step 2: Generate detailed findings
    findings_text, findings_in_tokens, findings_out_tokens = query_findings_expansion_with_gemini(
        client,
        analysis_summary=analysis_text,
        inventory_snapshot=data_text,
        config=config
    )

    return {
        "analysis_text": analysis_text,
        "findings_text": findings_text,
        "tokens": (analysis_in_tokens, analysis_out_tokens, findings_in_tokens, findings_out_tokens)
    }

def generate_ai_summary(spreadsheet_id: Optional[str], file_path: Optional[str], provider: str, output_to_excel: bool,ai_summary_platform: str):
    """
    Main function to run the Gemini integration.
    """
    if ai_summary_platform == "AWS":
        config = importlib.import_module("ai.config_aws")
    elif ai_summary_platform == "GCP":
        config = importlib.import_module("ai.config_gcp")
    else:
        raise ValueError(f"Unsupported ai_summary_platform: {ai_summary_platform}")

    temp_dir = os.path.join(config.PWD, "temp")
    os.makedirs(temp_dir, exist_ok=True)
    output_filename = ""
    logger.info("🚀 Gemini Integration (Threaded)\n" + "=" * 50)
    total_input_tokens_lt_200000 = 0
    total_output_tokens_lt_200000 = 0
    total_input_tokens_gt_200000 = 0
    total_output_tokens_gt_200000 = 0
    total_cost = 0
    try:
        integration = GeminiIntegration(config, provider=provider)
        
        if file_path:
            logger.info(f"\n📊 Reading data from Excel file: {file_path}...")
            data = integration.excel_read(file_path)
        elif spreadsheet_id:
            logger.info(f"\n📊 Reading data from Google Sheet: {spreadsheet_id}...")
            data = integration.gsheets_read(spreadsheet_id=spreadsheet_id)
        else:
            raise ValueError("Either a spreadsheet ID or a file path must be provided.")

        logger.info(f"✅ Loaded: {data['spreadsheet_title']} | 📋 Rows: {data['total_rows']} | 📄 Sheets: {data['sheet_count']}")

        logger.info("\n🔄 Grouping data by resource type...")
        grouped_data = group_data_by_resource(data)
        logger.info(f"Found {len(grouped_data)} resources to analyze: {list(grouped_data.keys())}")

        all_analysis_results = []
        
        logger.info("\n🤖 Running Gemini analysis in parallel...")
        all_analysis_results = []
        all_findings_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=30) as executor:
            future_to_resource = {
                executor.submit(process_single_resource_group, integration.gemini_client, provider, name, rows, config): name
                for name, rows in grouped_data.items()
            }

            for future in concurrent.futures.as_completed(future_to_resource):
                resource_name = future_to_resource[future]
                try:
                    result_dict = future.result()
                    if result_dict["analysis_text"]:
                        all_analysis_results.append(result_dict["analysis_text"])
                    if result_dict["findings_text"]:
                        all_findings_results.append(result_dict["findings_text"])
                    
                    # Unpack and add tokens
                    analysis_in, analysis_out, findings_in, findings_out = result_dict["tokens"]
                    total_input_tokens_lt_200000 += analysis_in + findings_in
                    total_output_tokens_lt_200000 += analysis_out + findings_out

                except Exception as exc:
                    logger.error(f'❌ {resource_name} generated an exception: {exc}')

        logger.info("\n🔗 Merging all analysis and findings results...")
        merged_analysis_text = "\n".join(all_analysis_results)
        merged_findings_text = "\n".join(all_findings_results)
        
        # --- Debugging Step: Save the raw findings response ---
        try:
            with open(os.path.join(temp_dir, "findings_response.txt"), "w") as f:
                f.write(merged_findings_text)
            logger.info("📝 Raw findings response saved to temp/findings_response.txt for debugging.")
        except Exception as e:
            logger.error(f"Could not save findings_response.txt: {e}")
        # --- End of Debugging Step ---

        if provider == "GCP":
            headers = config.GCP_SUMMARY_HEADERS
        elif provider == "AZURE":
            headers = config.AZURE_SUMMARY_HEADERS
        elif provider == "AWS":
            headers = config.AWS_SUMMARY_HEADERS
        elif provider == "OCI":
            headers = config.OCI_SUMMARY_HEADERS
        elif provider == "KUBE":
            headers = config.KUBE_SUMMARY_HEADERS
        final_parsed_data = [headers]
        final_parsed_data.extend(integration.parse_pipe_delimited_response(merged_analysis_text))

        if output_to_excel:
            if file_path:
                # base, ext = os.path.splitext(os.path.basename(file_path))
                # output_filename = os.path.join(config.PWD, "temp", f"{base}_with_summary{ext}")
                # shutil.copy(file_path, output_filename)
                output_filename = file_path
                
                logger.info(f"\n📝 Writing final analysis to a new sheet in: {output_filename}...")
                # Load workbook and remove the sheet if it already exists
                wb = load_workbook(output_filename)

                if config.SUMMARY_SHEET_NAME in wb.sheetnames:
                    std = wb[config.SUMMARY_SHEET_NAME]
                    wb.remove(std)

                # Create a new sheet
                ws = wb.create_sheet(title=config.SUMMARY_SHEET_NAME)

                # Write header
                for col_index, header in enumerate(final_parsed_data[0], start=1):
                    ws.cell(row=1, column=col_index, value=header)

                # Write data rows
                for row_index, row_data in enumerate(final_parsed_data[1:], start=2):
                    for col_index, cell_value in enumerate(row_data, start=1):
                        ws.cell(row=row_index, column=col_index, value=cell_value)

                # Save workbook
                wb.save(output_filename)
                logger.info(f"✅ Analysis summary sheet added to {output_filename}")

                # Write the new findings to a separate sheet
                logger.info(f"\n📝 Writing detailed findings to a new sheet in: {output_filename}...")
                wb = load_workbook(output_filename)
                findings_sheet_name = "Analysis_Findings"
                if findings_sheet_name in wb.sheetnames:
                    del wb[findings_sheet_name]
                ws_findings = wb.create_sheet(title=findings_sheet_name)
                
                # Use the dedicated parser for the findings sheet
                parsed_findings = parse_findings_response(merged_findings_text)
                
                # Write header and data for findings
                if parsed_findings:
                    for col_index, header in enumerate(parsed_findings[0], start=1):
                        ws_findings.cell(row=1, column=col_index, value=header)
                    for row_index, row_data in enumerate(parsed_findings[1:], start=2):
                        for col_index, cell_value in enumerate(row_data, start=1):
                            ws_findings.cell(row=row_index, column=col_index, value=cell_value)
                
                wb.save(output_filename)
                logger.info(f"✅ Detailed findings sheet added to {output_filename}")

            else:
                # If input was a Google Sheet, create a new Excel file
                output_filename = os.path.join(config.PWD, "temp", "analysis_summary.xlsx")
                logger.info(f"\n📝 Writing final analysis to Excel file: {output_filename}...")
                df = pd.DataFrame(final_parsed_data[1:], columns=final_parsed_data[0])
                df.to_excel(output_filename, index=False, sheet_name=config.SUMMARY_SHEET_NAME)
                logger.info(f"✅ Analysis saved to {output_filename}")
        elif spreadsheet_id:
            logger.info("\n📝 Writing final analysis to spreadsheet...")
            integration.create_summary_sheet(spreadsheet_id, config.SUMMARY_SHEET_NAME)
            integration.sheets_service.spreadsheets().values().clear(
                spreadsheetId=spreadsheet_id,
                range=f"{config.SUMMARY_SHEET_NAME}!A:Z"
            ).execute()
            integration.sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=f"{config.SUMMARY_SHEET_NAME}!A1",
                valueInputOption='RAW',
                body={"values": final_parsed_data}
            ).execute()
            with open(os.path.join(config.PWD, "temp", "response_threaded.txt"), "w") as f:
                f.write(merged_analysis_text)
            logger.info(f"✅ Response written and saved. Now formatting headers...")
            integration.format_summary_sheet_headers(spreadsheet_id, config.SUMMARY_SHEET_NAME)

            # Write the new findings to a separate sheet in Google Sheets
            logger.info("\n📝 Writing detailed findings to a new Google Sheet...")
            integration.create_summary_sheet(spreadsheet_id, "Analysis_Findings")
            # Use the dedicated parser for the findings sheet
            parsed_findings = parse_findings_response(merged_findings_text)
            if parsed_findings:
                integration.sheets_service.spreadsheets().values().clear(
                    spreadsheetId=spreadsheet_id,
                    range="Analysis_Findings!A:Z"
                ).execute()
                integration.sheets_service.spreadsheets().values().update(
                    spreadsheetId=spreadsheet_id,
                    range="Analysis_Findings!A1",
                    valueInputOption='RAW',
                    body={"values": parsed_findings}
                ).execute()
                integration.format_summary_sheet_headers(spreadsheet_id, "Analysis_Findings")
            logger.info("✅ Detailed findings sheet created and formatted.")

        # Pricing for gemini-2.5-pro model
        input_cost_lt_200000 = (total_input_tokens_lt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["input_tokens_lt_200000"]
        output_cost_lt_200000 = (total_output_tokens_lt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["output_tokens_lt_200000"]
        total_cost_lt_200000 = input_cost_lt_200000 + output_cost_lt_200000

        input_cost_gt_200000 = (total_input_tokens_gt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["input_tokens_gt_200000"]
        output_cost_gt_200000 = (total_output_tokens_gt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["output_tokens_gt_200000"]
        total_cost_gt_200000 = input_cost_gt_200000 + output_cost_gt_200000
        total_cost = total_cost_lt_200000 + total_cost_gt_200000
        logger.info("\n📊 Token Usage & Cost Analysis (gemini-2.5-pro)")
        logger.info("-" * 50)
        logger.info(f"Total Input Tokens (lt 200,000):  {total_input_tokens_lt_200000:,}")
        logger.info(f"Total Output Tokens (lt 200,000): {total_output_tokens_lt_200000:,}")
        logger.info(f"Total Input Tokens (gt 200,000):  {total_input_tokens_gt_200000:,}")
        logger.info(f"Total Output Tokens (gt 200,000): {total_output_tokens_gt_200000:,}")
        logger.info(f"Total Tokens:        {(total_input_tokens_lt_200000 + total_output_tokens_lt_200000 + total_input_tokens_gt_200000 + total_output_tokens_gt_200000):,}")
        logger.info("-" * 50)
        logger.info(f"Input Cost (lt 200,000):            ${input_cost_lt_200000:.6f}")
        logger.info(f"Output Cost (lt 200,000):           ${output_cost_lt_200000:.6f}")
        logger.info(f"Input Cost (gt 200,000):            ${input_cost_gt_200000:.6f}")
        logger.info(f"Output Cost (gt 200,000):           ${output_cost_gt_200000:.6f}")
        logger.info(f"💰 Total Estimated Cost: ${total_cost:.6f}")
        logger.info("-" * 50)
        logger.info("\n🎉 Integration complete!")

    except Exception as error:
        logger.error(f"❌ Error: {error}")
        # Pricing for gemini-2.5-pro model
        input_cost_lt_200000 = (total_input_tokens_lt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["input_tokens_lt_200000"]
        output_cost_lt_200000 = (total_output_tokens_lt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["output_tokens_lt_200000"]
        total_cost_lt_200000 = input_cost_lt_200000 + output_cost_lt_200000

        input_cost_gt_200000 = (total_input_tokens_gt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["input_tokens_gt_200000"]
        output_cost_gt_200000 = (total_output_tokens_gt_200000 / 1_000_000) * config.GEMINI_MODEL_PRICING["output_tokens_gt_200000"]
        total_cost_gt_200000 = input_cost_gt_200000 + output_cost_gt_200000
        total_cost = total_cost_lt_200000 + total_cost_gt_200000
        logger.info("\n📊 Token Usage & Cost Analysis (gemini-2.5-pro)")
        logger.info("-" * 50)
        logger.info(f"Total Input Tokens (lt 200,000):  {total_input_tokens_lt_200000:,}")
        logger.info(f"Total Output Tokens (lt 200,000): {total_output_tokens_lt_200000:,}")
        logger.info(f"Total Input Tokens (gt 200,000):  {total_input_tokens_gt_200000:,}")
        logger.info(f"Total Output Tokens (gt 200,000): {total_output_tokens_gt_200000:,}")
        logger.info(f"Total Tokens:        {(total_input_tokens_lt_200000 + total_output_tokens_lt_200000 + total_input_tokens_gt_200000 + total_output_tokens_gt_200000):,}")
        logger.info("-" * 50)
        logger.info(f"Input Cost (lt 200,000):            ${input_cost_lt_200000:.6f}")
        logger.info(f"Output Cost (lt 200,000):           ${output_cost_lt_200000:.6f}")
        logger.info(f"Input Cost (gt 200,000):            ${input_cost_gt_200000:.6f}")
        logger.info(f"Output Cost (gt 200,000):           ${output_cost_gt_200000:.6f}")
        logger.info(f"💰 Total Estimated Cost: ${total_cost:.6f}")
        logger.info("\n🎉 Integration complete!")

        return file_path, total_cost

    if output_to_excel:
        return output_filename, total_cost
    else:
        return spreadsheet_id, total_cost


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Gemini Integration for Google Sheets and Excel.")
    parser.add_argument("--spreadsheet-id", help="The ID of the Google Sheet to process.")
    parser.add_argument("--file-path", help="The path to the local Excel file to process.")
    parser.add_argument("--provider", help="The cloud provider (GCP or AZURE).", default="GCP")
    parser.add_argument("--output-to-excel", action="store_true", help="Write the output to an Excel file instead of Google Sheets.")
    args = parser.parse_args()

    if not args.spreadsheet_id and not args.file_path:
        parser.error("Either --spreadsheet-id or --file-path must be provided.")

    generate_ai_summary(args.spreadsheet_id, args.file_path, args.provider, args.output_to_excel)
