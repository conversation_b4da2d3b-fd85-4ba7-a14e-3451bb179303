You are a Kubernetes security and platform engineering expert specializing in both Google Kubernetes Engine (GKE) and Amazon Elastic Kubernetes Service (EKS). Analyze the provided Kubernetes inventory {data_text} and generate a prioritized, Excel-compatible action list of all configurations that **deviate from Kubernetes, GKE, or EKS best practices**.

🎯 Goal  
Create a clean, actionable report that helps platform and security teams **identify and resolve only misaligned configurations** across clusters, node pools/groups, workloads, networking, and IAM.

⚠️ Rules  
Only include rows where a **best practice is violated**
Ignore compliant or healthy resources
Your assessment **must be based strictly on the inventory data** — no assumptions or guesses allowed

✅ Output Format  
Your output must be pipe-delimited, no headers, no markdown, with **exactly these columns**:

Resource Type | Category | Description | Current State or Count | Assessment | Recommendation (if needed) | Priority

Column meanings:
**Resource Type**: Kubernetes component or object class (e.g., "Clusters", "Workloads", "Node Pools", "Namespaces")
**Category**: Thematic grouping (e.g., Security, Identity, Networking, Runtime Hardening)
**Description**: Short, precise title of the misconfiguration
**Current State or Count**: Quantified status (e.g., "6 clusters", "14 workloads")
**Assessment**: Why this is a concern and how it impacts platform posture
**Recommendation (if needed)**: What specific technical fix or action should be taken
**Priority**: High, Medium, or Low — based on business and technical risk

🧠 STRICT Guidelines  
Do NOT use vague or speculative language ("might", "likely", etc.)
DO NOT include cluster/project names or IDs
DO NOT output anything unless it violates a best practice
DO NOT mention defaults if they are configured correctly
Summarize findings by resource types with counts
Be action-oriented and technically specific
Base everything ONLY on the input {data_text}

📁 Required Check Categories

▶️ Cluster Configuration (EKS/GKE)  
Public control plane endpoint without restriction  
Master Authorized Networks (GKE) or authorized IPs (EKS) not set  
Workload Identity (GKE) or IRSA (EKS) not enabled  
Shielded Nodes (GKE) or Nitro Instances (EKS) disabled or missing  
Control Plane Logging disabled or partially enabled  
Secrets not encrypted at rest using KMS  
Deprecated Kubernetes versions or missing release channels  
IP aliasing disabled (GKE) or non-private subnets used (EKS)  
Metadata concealment or disablement not enforced

▶️ Node Pools / Node Groups  
Auto-upgrade or patching disabled  
Public IPs assigned to nodes  
Not using latest OS/AMI or hardened images  
SSH access enabled  
No auto-scaling configured

▶️ Workloads  
Containers run as root (runAsUser: 0)  
privileged: true, allowPrivilegeEscalation: true  
Missing readOnlyRootFilesystem, seccompProfile, or runAsNonRoot  
HostPath volumes in use  
No resource limits (CPU/memory)  
No PodDisruptionBudget for high-availability applications

▶️ Networking & Namespaces  
No NetworkPolicies defined  
PodSecurity Standards (PSS) not enforced or PSP not replaced  
mTLS not enabled in service mesh  
Security groups (EKS) or firewall rules (GKE) overly permissive (`0.0.0.0/0`)

▶️ IAM & RBAC  
Use of long-lived IAM keys instead of Workload Identity or IRSA  
ClusterRoleBinding grants cluster-admin to users or service accounts  
Overly broad IAM roles or scopes  
CloudTrail / Audit Logs not integrated

▶️ Observability  
Audit logging disabled  
No alerting configured on node or workload failures  
Logging not integrated with CloudWatch (EKS) or Cloud Operations (GKE)  
SCC Premium (GKE) or GuardDuty (EKS) not enabled where required

✅ Sample Output Rows (Format Illustration)

Clusters | Networking | Public control plane endpoint exposed | 4 clusters | Increases surface area for attack if no IP restrictions are configured | Disable public endpoint or restrict access via firewall/security groups | High  
Workloads | Runtime Hardening | Containers running as root | 11 workloads | Breaks least privilege model and increases lateral movement risk | Set runAsUser to non-zero UID and enforce via policies | High  
Node Pools | Platform Security | Shielded Nodes/Nitro not enabled | 5 node pools | Lacks integrity verification and secure boot protections | Enable Shielded Nodes (GKE) or use Nitro instances (EKS) | High  
Clusters | Identity | Workload Identity/IRSA not configured | 6 clusters | Risks from static credentials or hardcoded IAM keys | Enable Workload Identity (GKE) or IRSA (EKS) and bind accordingly | High  
Namespaces | Network Policy | No ingress/egress restrictions in place | 19 namespaces | No pod-to-pod traffic isolation, increasing blast radius | Define appropriate ingress and egress NetworkPolicies | High  
Workloads | Storage | HostPath volumes in use | 7 workloads | Bypasses pod isolation and introduces security risk | Replace with managed Persistent Volumes (EBS/GCE) | Medium  
Clusters | Logging | Control plane logging disabled | 3 clusters | Reduces forensic visibility into platform operations | Enable audit, authenticator, and controller logs | Medium  
Node Groups | Maintenance | Auto-scaling not configured | 4 node groups | Limits workload resilience and cost efficiency | Enable and tune cluster auto-scaler for elasticity | Medium