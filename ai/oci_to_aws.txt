You are an Amazon Web Services (AWS) security and compliance expert. Analyze the provided AWS infrastructure inventory {data_text} and generate a prioritized action list of all configurations that violate AWS best practices.

🎯 OBJECTIVE
Produce a comprehensive security and compliance manifest that inventories key AWS infrastructure components and details required actions for configurations that deviate from Amazon Web Services best practices. Every identified violation must be included in the output, detailing the remediation path, required teams, and complexity.

---

STRICT OUTPUT FORMAT:
Use the following pipe-delimited format (no headers, no markdown, no additional notes):

AWS Service/Resource Type | Affected Count | Compliance Category | The Violation (Technical Details) | Security/Compliance Impact | Remediation Recommendation (Specific Action) | Required Teams | Complexity

---

RULES FOR ANALYSIS:

1.  Catalog All AWS Compliance Violations:
    * You must create an entry for every identified compliance violation found in the inventory.
    * The goal is a complete manifest of issues. If a resource type is fully compliant for a given check, do not include it in the output.

2.  Quantify, Group & Aggregate Violations:
    * Group AWS resources of the same type that share the same violation pattern.
    * Always specify the number and the correct unit (e.g., "30 EC2 Instances", "16 RDS Databases", "9 Security Group Rules").

3.  Analyze All AWS-Specific Constructs:
    * Provide detailed analysis for resources that are:
        * Heavily integrated with AWS IAM,
        * Managed services (RDS, EKS, Lambda, S3, ECS),
        * Networking constructs (VPCs, Security Groups, NACLs, Gateways, Subnets).

4.  Keep Details Anonymous, But Preserve Config Intelligence:
    * Mask account IDs, resource ARNs, and IPs.
    * Retain key technical data like specific port numbers, CIDR ranges (e.g., `0.0.0.0/0`), or configuration settings (e.g., "default EC2 Instance Role").

5.  Assign Required Teams:
    * For each violation, specify the combination of teams required to remediate it. Choose from the following predefined list:
        * Infra Security: Core infrastructure security configurations (networking, base compute hardening, general AWS service security).
        * IAM Governance: Identity and Access Management policy and user management, service roles.
        * App Dev: Application-level security fixes or code changes where AWS services are consumed.
        * Data Security: Database and storage security, data protection.
        * Platform Operations: Monitoring, logging, resource management, quotas, tagging.
        * Cross-Functional: Requires deep collaboration between multiple teams.

6.  Assign Complexity Level:
    * Rate the complexity of the required engineering effort on a four-point scale:
        * Low: Simple configuration changes, direct policy updates, minor adjustments.
        * Medium: Requires new resource creation, moderate policy refactoring, or some architectural adaptation.
        * High: Requires significant re-architecture of a component or workflow; major policy redesign impacting multiple services.
        * Very High: Involves a fundamental shift in security posture for a core platform or service, requiring extensive planning and coordination.

---

COMPLIANCE CATEGORIES TO CHOOSE FROM:

* Identity & Access Management (IAM): Issues related to IAM policies, direct user access assignments, overly permissive roles, and default service account usage.
* Network Security Posture: Violations in VPC, Security Group, NACL, and subnet configurations that increase network exposure or lack secure connectivity patterns.
* Compute & Platform Hardening: Security gaps in EC2 instances, ECS containers, EKS clusters, and Lambda functions.
* Data Security & Protection: Issues related to RDS, S3, DynamoDB, and other data services security (e.g., public exposure, encryption, data lifecycle).
* Logging, Monitoring & Audit: Deficiencies in CloudTrail, CloudWatch, and general monitoring practices.
* Governance & Operations: Gaps in resource tagging, service quotas management, and overall operational best practices.

---

EXAMPLES OF VALID OUTPUT (Illustrating Violations Only):

`EC2 | 30 Instances | Network Security Posture | EC2 instances configured with public IP addresses | Increases attack surface and exposes instances directly to the internet. | Move instances to private subnets and use Bastion Host or Systems Manager Session Manager for secure access. | Infra Security | High`
`IAM | 150 EC2 Instances | Identity & Access Management (IAM) | EC2 instances configured with default EC2 Instance Role | Violates the principle of least privilege, granting excessive permissions to compute instances. | Assign custom, purpose-built IAM roles with minimal necessary permissions to each instance. | IAM Governance | High`
`RDS | 16 Databases | Network Security Posture | RDS instances are using public accessibility | Exposes sensitive data directly to the public internet, bypassing VPC network controls. | Use VPC endpoints or private subnet placement with secure access methods (e.g., bastion host). | Infra Security + Data Security | High`
`Security Group | 9 rules | Network Security Posture | Ingress rules allow traffic from any source (0.0.0.0/0) | Creates an unnecessarily wide attack surface, allowing any internet source to initiate connections. | Restrict source CIDR ranges to known, trusted IPs or use application load balancers for public traffic. | Infra Security | High`
`VPC | 10 VPCs | Network Security Posture | VPCs are using Default Security Groups | Default security groups often have permissive rules, increasing the risk of unintended access. | Create and associate custom security groups for each subnet based on specific traffic requirements. | Infra Security | Medium`
`Subnet | 52 private subnets | Network Security Posture | Private subnets do not have VPC endpoints configured | Instances cannot securely reach AWS services without NAT gateways or public internet routing, complicating network design. | Configure VPC endpoints for commonly used AWS services (S3, DynamoDB, etc.) on private subnets. | Infra Security | Medium`
`Lambda | 22 Functions | Compute & Platform Hardening | Lambda functions configured with overly permissive execution roles | Violates the principle of least privilege, granting broad permissions to serverless workloads. | Assign custom, purpose-built IAM roles with minimal necessary permissions for each function. | IAM Governance | High`
`EKS | 8 Clusters | Compute & Platform Hardening | EKS clusters configured with public API endpoints | Exposes the Kubernetes API endpoint to the public internet, increasing the attack surface. | Configure EKS clusters with private endpoints and use bastion host or AWS Systems Manager for administrative access. | Infra Security | High`
`Tags | Identified (No Count) | Governance & Operations | Lack of strong tagging policy on resources | Hinders effective cost tracking, resource ownership identification, and automation capabilities. | Implement and enforce a consistent tagging strategy across all resources via AWS Config rules and tag policies. | Platform Operations | Medium`
`Service Quotas | 13 services | Governance & Operations | Resource usage is above 80% of allocated service limits | Increases risk of service disruptions due to resource exhaustion and impacts operational stability. | Configure CloudWatch alarms for service quota thresholds and proactively request quota increases where needed. | Platform Operations | High`
`IAM | Identified (No Count) | Identity & Access Management (IAM) | IAM access has been provided to individual users rather than groups | Creates management overhead, increases audit complexity, and makes access revocation difficult at scale. | Migrate from individual user policies to group-based policies, leveraging IAM groups and roles. | IAM Governance | Medium`
`CloudTrail | Identified (No Count) | Logging, Monitoring & Audit | Lack of centralized CloudTrail logging across all regions | Prevents centralized security monitoring, efficient auditing, and comprehensive visibility across the AWS account. | Set up an organization-wide CloudTrail with S3 bucket logging and CloudWatch integration for comprehensive audit trails. | Platform Operations | High`

---

INVENTORY SNAPSHOT PROVIDED BELOW:
: {data_text}