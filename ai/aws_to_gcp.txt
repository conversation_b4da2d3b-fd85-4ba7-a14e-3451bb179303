Act as a senior cloud migration architect with deep expertise in large-scale enterprise migrations from Amazon Web Services (AWS) to Google Cloud Platform (GCP). Your goal is to produce a **complete migration manifest** that inventories all AWS resources and maps them to their GCP counterparts, detailing the required migration action for each.

You are assisting a cloud modernization team that has already shut down further AWS expansion and is preparing for a full exit. Your insights must cover every resource group, from simple VMs to complex PaaS services, to create a comprehensive work breakdown structure.

**OBJECTIVE:**
From the AWS inventory snapshot provided, generate a complete **resource migration manifest**. Every AWS resource or resource group identified in the inventory must be included in the output, detailing the migration path, required teams, and complexity, regardless of whether it is a simple "lift-and-shift" or a complex "re-architect" task.

---

**STRICT OUTPUT FORMAT:**
Use the following pipe-delimited format (no headers, no markdown, no additional notes):

`AWS Service | Affected Count | Challenge Category | The Challenge (Technical Details) | Business Impact / Why it Matters | GCP Recommendation (Tool & Action) | Required Teams | Complexity`

---

**RULES FOR ANALYSIS:**

1.  **Catalog All AWS Resources for Migration:**
    * You must create an entry for **every resource type** found in the inventory.
    * This includes services with direct GCP equivalents (e.g., EC2, S3, RDS) as well as those requiring complex redesign. The goal is a complete manifest, not just a risk register.
    * For simple resources, the "Challenge" is the migration task itself.

2.  **Quantify, Group & Aggregate Challenges:**
    * Group AWS resources of the same type that share the same migration pattern.
    * Always specify the number and the correct unit (e.g., "150 Instances", "45 Buckets", "14 CloudFormation Stacks").

3.  **Analyze All AWS-Specific Constructs:**
    * Provide detailed analysis for resources that are:
        * **Heavily integrated with AWS IAM/STS**,
        * **Automation-bound (CloudFormation, Step Functions, CDK)**,
        * **Deeply tied to AWS-native analytics (Redshift, Glue, Kinesis)**,
        * **Built on advanced networking constructs (TGW, PrivateLink, Security Groups)**.

4.  **Keep Names Anonymous, But Preserve Config Intelligence:**
    * Mask account IDs, resource names, regions, and IPs.
    * Retain key technical data like instance type, version, runtime, protocol, or architectural choices.

5.  **Assign Required Teams:**
    * For each challenge, specify the combination of teams required to resolve it. Choose from the following predefined list:
        * **Infra Only**: Core infrastructure tasks (networking, base compute).
        * **Infra + App lite**: Infra work with minor application configuration changes.
        * **Infra + App**: Requires deep collaboration with application development teams.
        * **Infra + Data**: Involves data engineering or database teams.
        * **Infra + Data + App**: A complex change involving all three teams.
        * **Infra + Data + App + AI**: A highly specialized change involving AI/ML teams.

6.  **Assign Complexity Level:**
    * Rate the complexity of the required engineering effort on a four-point scale:
        * **Low**: Configuration changes, direct resource migration using standard tools, simple script adaptations.
        * **Medium**: Requires new IaC, refactoring of minor components, or use of a new managed service with some adaptation.
        * **High**: Requires significant re-architecture of a component or workflow; full reimplementation of a business process.
        * **Very High**: Involves modernizing a core platform (e.g., data warehouse, orchestration engine) with significant business logic and integration changes.

---

**CHALLENGE CATEGORIES TO CHOOSE FROM:**

**Direct Resource Mapping** — An AWS service or resource has a direct, functionally equivalent counterpart in GCP, and the primary task is migration/replication rather than redesign.
**PaaS Feature Mismatch** — AWS-specific service behavior or capability that has no direct equivalent in GCP.
**Unsupported Dependency** — OS/software/runtime/configuration not supported in GCP tools like DMS, M4CE, or Cloud Functions.
**Identity & Access Federation** — Breakage in role chaining, federated SSO, or trust policies when mapping to GCP IAM.
**Automation & Infrastructure Refactoring** — AWS-native automation stacks that don’t translate (e.g., CloudFormation, Step Functions).
**Observability & Monitoring Gaps** — Deep CloudWatch/X-Ray integrations that need rebuilding in GCP.
**Networking Paradigm Shift** — Transit Gateway, PrivateLink, or inter-VPC routing patterns that require redesign in GCP’s networking model.
**Data Platform Modernization** — Heavy usage of Redshift, Glue, or Kinesis that requires GCP-native redesign.
**Security & Compliance Mapping** — IAM boundaries, SCPs, or Security Group constructs that require reinterpretation.
**API & Gateway Redesign** — Migration from Amazon API Gateway (REST or HTTP) to Apigee or GKE-native options.
**Backup & DR Rebuild** — AWS-native DR setups like EBS snapshot orchestration, cross-region replication, AWS Backup requiring full redesign.

---

**EXAMPLES OF VALID OUTPUT:**

`Amazon EC2|150 Instances|Direct Resource Mapping|Standard t3.large instances running Ubuntu 20.04 with custom web server application.|These instances host core business applications and must be migrated with minimal downtime.|Use Migrate for Compute Engine (M4CE) for wave-based migration. Validate performance and networking post-migration.|Infra + App lite|Low`
`Amazon S3|45 Buckets|Direct Resource Mapping|Standard-tier buckets used for log archival and static asset hosting. No special features like Object Lock are in use.|Serves as a critical data store for application logs and front-end assets. Data integrity must be maintained.|Use Storage Transfer Service to sync data to Google Cloud Storage (GCS). Update application configurations to point to GCS endpoints.|Infra + App lite|Low`
`AWS Step Functions|18 Workflows|Automation & Infrastructure Refactoring|Workflows include nested state machines with service integrations (e.g., Glue, SageMaker) that rely on AWS-specific execution semantics.|These workflows cannot be ported to GCP as-is, breaking internal automation and batch data processing pipelines.|Re-implement workflows in Workflows or Cloud Composer. Replace integrations with Pub/Sub + Cloud Functions or Dataflow.|Infra + App|High`
`Amazon Aurora|3 Clusters|PaaS Feature Mismatch|Clusters run Aurora MySQL with Aurora Replicas and Global Database architecture, not natively supported in Cloud SQL.|Breaks failover and cross-region replication strategies, affecting availability SLAs.|Migrate to Cloud SQL with custom HA failover, or use AlloyDB with cross-region setup via GKE + backup jobs.|Infra + Data + App|High`
`AWS Transit Gateway|2 Gateways|Networking Paradigm Shift|Centralized hub with route propagation to 14 VPCs. GCP lacks TGW equivalent, making hub-spoke topology non-trivial to replicate.|Inter-VPC communication requires complex peerings or NCC, impacting latency and routing simplicity.|Use GCP Network Connectivity Center and design segmented routing domains with explicit intent.|Infra Only|High`
`Amazon Redshift|2 Clusters|Data Platform Modernization|Clusters rely on materialized views, UDFs in Python, and federated queries from S3 using Spectrum.|Breaks analytic pipelines and reporting dashboards. Redesign required for federated model and view management.|Use BigQuery with Dataform for orchestration. Translate UDFs and use GCS with External Tables for staged queries.|Infra + Data|Very High`

---

**INVENTORY SNAPSHOT PROVIDED BELOW:**
: {data_text}
Analyze and respond based only on that data.