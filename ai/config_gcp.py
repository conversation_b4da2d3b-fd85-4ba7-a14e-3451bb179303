# config.py
import os

PWD = os.path.dirname(__file__)

ALLOWED_USERS = ["<EMAIL>",
                 "<EMAIL>",
                 "<EMAIL>",
                 "<EMAIL>",
                 "<EMAIL>",
                ]

# Google Sheets Configuration
SUMMARY_SHEET_NAME = "Analysis_Summary"

# Google Cloud / Gemini Configuration
DEFAULT_PROJECT_ID = "ce-ps3"
DEFAULT_LOCATION = "us-central1"
GEMINI_MODEL = "gemini-2.5-pro"
GEMINI_MODEL_PRICING = {
    "input_tokens_lt_200000": 1.25,
    "output_tokens_lt_200000": 10.00,
    "input_tokens_gt_200000": 2.50,
    "output_tokens_gt_200000": 15.00
}

GCP_SUMMARY_HEADERS = [
    "Resource Type", "Category", "Description", "Current State or Count",
    "Assessment", "Recommendation (if needed)", "Priority"
]

AZURE_SUMMARY_HEADERS = [
    "Azure Service","Affected Count","Challenge Category","The Challenge (Technical Details)",
    "Business Impact / Why it Matters","GCP Recommendation (Tool & Action)","Priority"
]

AWS_SUMMARY_HEADERS = [
    "Aws Service","Affected Count","Challenge Category","The Challenge (Technical Details)",
    "Business Impact / Why it Matters","GCP Recommendation (Tool & Action)","Required Teams","Complexity"
]

OCI_SUMMARY_HEADERS = [
     "OCI Service","Affected Count","Challenge Category","The Challenge (Technical Details)",
    "Business Impact / Why it Matters","GCP Recommendation (Tool & Action)","Priority"
]

KUBE_SUMMARY_HEADERS = [ 
    "Resource Type", "Category", "Description", "Current State or Count", "Assessment", "Recommendation (if needed)", "Priority"
]

def load_prompt(filename):
    with open(filename, 'r') as f:
        return f.read()

GCP_GEMINI_PROMPT = load_prompt(os.path.join(PWD, 'gcp_prompt.txt'))
AZURE_GEMINI_PROMPT = load_prompt(os.path.join(PWD, 'azure_to_gcp.txt'))
AWS_GEMINI_PROMPT = load_prompt(os.path.join(PWD, 'aws_to_gcp.txt'))
KUBE_GEMINI_PROMPT = load_prompt(os.path.join(PWD, 'kube_prompt.txt'))
OCI_GEMINI_PROMPT = load_prompt(os.path.join(PWD, 'oci_to_gcp.txt'))

