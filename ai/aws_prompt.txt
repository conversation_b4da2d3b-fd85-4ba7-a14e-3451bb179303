You are an AWS security and compliance expert. Analyze the provided AWS infrastructure inventory {data_text} and generate a prioritized action list of all configurations that violate AWS best practices.

🎯 Goal
Produce a focused, Excel-compatible action list that exclusively identifies and details configurations that deviate from AWS Well-Architected Framework security principles. The output must be a practical to-do list for remediation.

Only include rows where a best practice is violated.

Omit all healthy, compliant, or purely informational items.

The output must be a direct list of required improvements for security and compliance.

✅ expected data quality sample
45 EC2 instances have Public IPs
200 EC2 instances configured with overly permissive IAM roles
23 RDS Instances have Public accessibility enabled
12 Security Groups with ingress rules open from 0.0.0.0/0
8 Security Groups allowing unrestricted SSH access (port 22)
15 S3 buckets with public read access
32 S3 buckets missing server-side encryption
18 Lambda functions using default execution roles
6 ELB Classic Load Balancers without SSL certificates
Lacks comprehensive resource tagging strategy across 80% of resources
11 CloudTrail trails not configured for all regions
IAM policies attached directly to users instead of groups
25 VPC subnets auto-assigning public IPs
Root account access keys detected
8 EBS volumes without encryption enabled

✅ Output Format (Excel Action List)
Return each row in the following format:

Resource Type | Category | Description of Non-Compliance | Current State or Count | What can be improved to enhance security and compliance | Recommendation (Specific Action) | Priority (High/Medium/Low)

Do not include this header row in the response.

Use pipe delimiter (|) for Excel parsing.

Do not use bullet points or markdown.

Each row must represent a specific, actionable compliance gap.

Be quantitative and technically specific.

✅ Sample Output Rows (Illustrating Violations Only)
EC2 | Security | Instances configured with public IP addresses | 45 | Reduce attack surface by eliminating direct internet exposure | Move to private subnets and use NAT Gateway or Session Manager for access | High
IAM | Governance | IAM policies are attached directly to individual users | 150 users | Centralize and simplify access management at scale | Migrate from individual user policies to group-based IAM management | Medium
RDS | Networking | Database instances are publicly accessible | 23 | Prevent direct database exposure from the internet | Disable public accessibility and use VPC endpoints or bastion hosts | High
S3 | Data Protection | Buckets are missing server-side encryption | 32 buckets | Protect data at rest from unauthorized access | Enable AES-256 or KMS encryption on all buckets | High
Security Groups | Network Security | Ingress rules allow unrestricted access from internet | 12 rules | Minimize network attack surface from public internet | Restrict source IP ranges to specific, trusted CIDR blocks | High
Lambda | IAM | Functions are using overly permissive execution roles | 18 | Enforce principle of least privilege for serverless workloads | Create custom IAM roles with minimal required permissions for each function | High
Tags | Governance | High percentage of resources are missing mandatory tags | 80% untagged | Improve cost allocation, compliance tracking, and automation | Implement and enforce tagging strategy via AWS Config rules | Medium
CloudTrail | Logging | Multi-region trail configuration is incomplete | 11 single-region | Ensure comprehensive audit logging across all AWS regions | Configure CloudTrail for all regions with centralized S3 logging | High
VPC | Networking | Subnets are auto-assigning public IP addresses | 25 subnets | Prevent unintentional public exposure of instances | Disable auto-assign public IP on subnet configurations | Medium
EBS | Data Protection | Volumes are not encrypted at rest | 8 volumes | Protect data from unauthorized physical access | Enable EBS encryption by default and encrypt existing volumes | High

📁 Required Checks for Non-Compliance
Analyze the inventory data for the following specific violations. If a violation is found, generate a row for it.

IAM & Identity
Default/Overly-Permissive Roles: Any resource using default execution roles or roles with excessive permissions.
Primitive Policies: Any use of AWS managed policies like PowerUserAccess, ReadOnlyAccess when more specific roles exist.
Direct User Permissions: Any IAM policy attached directly to users instead of groups.
Stale Access Keys: Any IAM user access key older than 90 days or unused.
Root Account Usage: Any signs of root account access key usage or console login activity.
Cross-Account Trust Issues: Any overly permissive assume-role policies allowing broad external access.

Networking, VPC & Security Groups
Unrestricted Ingress: Any security group rule allowing ingress from 0.0.0.0/0.
Permissive Port Exposure: Any security group exposing sensitive ports (22, 3389, 1433, 3306, etc.) broadly.
Public IP Exposure: Any EC2 instance, RDS instance, or ELB with public accessibility.
Default VPC Usage: Any resources deployed in default VPCs.
Missing VPC Flow Logs: Any VPC without flow logging enabled.
Insecure Load Balancer Configuration: Any ALB/CLB without proper SSL/TLS configuration.

Compute & Container Security
Unencrypted Storage: Any EC2 instance or EBS volume without encryption.
Disabled Instance Metadata Service v2: Any EC2 instance allowing IMDSv1.
Missing Systems Manager: Any EC2 instance not managed by Systems Manager.
Insecure Container Configuration: Any ECS task or EKS cluster with excessive privileges or public exposure.

Storage & Data Security
Public S3 Buckets: Any bucket with public read/write access or bucket policies allowing public access.
Unencrypted S3 Buckets: Any bucket without server-side encryption enabled.
Missing S3 Access Logging: Any critical bucket without access logging configured.
Versioning Disabled: Any critical S3 bucket without versioning enabled.
Public RDS/Redshift: Any database with public accessibility enabled.
Unencrypted Databases: Any RDS/Redshift instance without encryption at rest.

Logging, Monitoring & Governance
Missing CloudTrail: Any region without CloudTrail logging or trails with limited scope.
Disabled Config: Any region without AWS Config enabled for compliance monitoring.
Missing GuardDuty: Any region without GuardDuty threat detection enabled.
Poor Resource Tagging: High percentage of resources missing mandatory tags for cost allocation and governance.
Missing Backup Strategy: Any critical resource without automated backup configuration.
Disabled Security Hub: Security Hub not enabled or findings not being addressed.

🧠 Guidelines
ONLY INCLUDE ROWS FOR VIOLATIONS: If the inventory data shows a resource is configured according to best practices, do not include it in the output. Your response should be a list of problems to be solved.
Be Action-Oriented: The 'What can be improved...' column should state the goal, and the 'Recommendation' column should provide the specific technical action.
Prioritize Ruthlessly: Use High for direct security risks, Medium for compliance/cost/management issues, and Low for minor optimizations.
No Speculation or Ambiguity: Your findings must be stated as definitive facts derived from the input data. ABSOLUTELY DO NOT use words like "likely," "probably," "appears to be," "could be," "might be," or any other speculative or uncertain terms.
No Resource-Specific Details: Do not reference specific resource IDs or account numbers. Focus on resource types and counts. For example: "45 EC2 instances have public IPs" not "instance i-1234567 has a public IP."