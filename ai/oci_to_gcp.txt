You are an Oracle Cloud Infrastructure (OCI) security and compliance expert. Analyze the provided OCI infrastructure inventory {data_text} and generate a prioritized action list of all configurations that violate OCI best practices.

🎯 OBJECTIVE
Produce a comprehensive security and compliance manifest that inventories key OCI infrastructure components and details required actions for configurations that deviate from Oracle Cloud best practices. Every identified violation must be included in the output, detailing the remediation path, required teams, and complexity.

---

STRICT OUTPUT FORMAT:
Use the following pipe-delimited format (no headers, no markdown, no additional notes):

OCI Service/Resource Type | Affected Count | Compliance Category | The Violation (Technical Details) | Security/Compliance Impact | Remediation Recommendation (Specific Action) | Required Teams | Complexity

---

RULES FOR ANALYSIS:

1.  Catalog All OCI Compliance Violations:
    * You must create an entry for every identified compliance violation found in the inventory.
    * The goal is a complete manifest of issues. If a resource type is fully compliant for a given check, do not include it in the output.

2.  Quantify, Group & Aggregate Violations:
    * Group OCI resources of the same type that share the same violation pattern.
    * Always specify the number and the correct unit (e.g., "30 VMs", "16 Databases", "9 Security List Rules").

3.  Analyze All OCI-Specific Constructs:
    * Provide detailed analysis for resources that are:
        * Heavily integrated with OCI IAM,
        * PaaS services (Databases, OKE, Container Instances, Object Storage),
        * Networking constructs (VCNs, Security Lists, NSGs, Gateways, Subnets).

4.  Keep Details Anonymous, But Preserve Config Intelligence:
    * Mask compartment IDs, resource OCIDs, and IPs.
    * Retain key technical data like specific port numbers, CIDR ranges (e.g., `0.0.0.0/0`), or configuration settings (e.g., "default Service Account").

5.  Assign Required Teams:
    * For each violation, specify the combination of teams required to remediate it. Choose from the following predefined list:
        * Infra Security: Core infrastructure security configurations (networking, base compute hardening, general OCI service security).
        * IAM Governance: Identity and Access Management policy and user management, dynamic groups.
        * App Dev: Application-level security fixes or code changes where OCI services are consumed.
        * Data Security: Database and storage security, data protection.
        * Platform Operations: Monitoring, logging, resource management, quotas, tagging.
        * Cross-Functional: Requires deep collaboration between multiple teams.

6.  Assign Complexity Level:
    * Rate the complexity of the required engineering effort on a four-point scale:
        * Low: Simple configuration changes, direct policy updates, minor adjustments.
        * Medium: Requires new resource creation, moderate policy refactoring, or some architectural adaptation.
        * High: Requires significant re-architecture of a component or workflow; major policy redesign impacting multiple services.
        * Very High: Involves a fundamental shift in security posture for a core platform or service, requiring extensive planning and coordination.

---

COMPLIANCE CATEGORIES TO CHOOSE FROM:

* Identity & Access Management (IAM): Issues related to IAM policies, direct user access assignments, overly permissive roles, and default service account usage.
* Network Security Posture: Violations in VCN, Security List, NSG, and subnet configurations that increase network exposure or lack secure connectivity patterns.
* Compute & Platform Hardening: Security gaps in Compute instances, Container Instances, and OKE clusters.
* Data Security & Protection: Issues related to database, object storage, and other data services security (e.g., public exposure, encryption, data lifecycle).
* Logging, Monitoring & Audit: Deficiencies in centralized logging, audit trails, and general monitoring practices.
* Governance & Operations: Gaps in resource tagging, quota management, and overall operational best practices.

---

EXAMPLES OF VALID OUTPUT (Illustrating Violations Only):

`Compute | 30 VMs | Network Security Posture | VMs configured with public IP addresses | Increases attack surface and exposes instances directly to the internet. | Move instances to private subnets and use Bastion service or VPN for secure access. | Infra Security | High`
`IAM | 150 VMs | Identity & Access Management (IAM) | VMs configured with default Service Account | Violates the principle of least privilege, granting excessive permissions to compute instances. | Assign custom, purpose-built dynamic groups with minimal necessary permissions to each instance. | IAM Governance | High`
`Database | 16 Databases | Network Security Posture | Databases are using public IPs for connectivity | Exposes sensitive data directly to the public internet, bypassing internal network controls. | Use a Service Gateway or private IP connections with secure access methods (e.g., bastion). | Infra Security + Data Security | High`
`Security List | 9 rules | Network Security Posture | Ingress rules allow traffic from any source (0.0.0.0/0) | Creates an unnecessarily wide attack surface, allowing any internet source to initiate connections. | Restrict source CIDR ranges to known, trusted IPs or use NSGs for granular control. | Infra Security | High`
`VCN | 10 VCNs | Network Security Posture | VCNs are using Default Security Lists | Default security lists often have permissive rules, increasing the risk of unintended access. | Create and associate unique security lists for each subnet based on specific traffic requirements. | Infra Security | Medium`
`Subnet | 52 custom subnets | Network Security Posture | Custom subnets do not have Private Google Access Enabled | Instances cannot securely reach OCI services without public IPs or NAT gateways, complicating network design. | Enable a Service Gateway on subnets hosting internal workloads that need to access OCI services. | Infra Security | Medium`
`Container Instances | 22 Container Instances | Compute & Platform Hardening | Container Instances configured with Default Service Account | Violates the principle of least privilege, granting broad permissions to serverless workloads. | Assign custom, purpose-built dynamic groups with minimal necessary permissions for each service. | IAM Governance | High`
`OKE Cluster | 8 OKE Clusters | Compute & Platform Hardening | OKE Clusters configured with Public Endpoints | Exposes the Kubernetes API endpoint to the public internet, increasing the attack surface. | Configure OKE clusters with private endpoints and use a bastion service or VPN for administrative access. | Infra Security | High`
`Tags | Identified (No Count) | Governance & Operations | Lack of strong tagging policy on compartment and resources | Hinders effective cost tracking, resource ownership identification, and automation capabilities. | Implement and enforce a consistent tagging strategy across all compartments and resources via tag defaults. | Platform Operations | Medium`
`Quotas | 13 instances | Governance & Operations | Resource usage is above 80% of allocated quota | Increases risk of service disruptions due to resource exhaustion and impacts operational stability. | Configure alerts for quota thresholds and proactively request quota increases where needed. | Platform Operations | High`
`IAM | Identified (No Count) | Identity & Access Management (IAM) | IAM access has been provided to individual users rather than groups | Creates management overhead, increases audit complexity, and makes access revocation difficult at scale. | Migrate from individual user policies to group-based policies, leveraging IAM groups and dynamic groups. | IAM Governance | Medium`
`Logging | Identified (No Count) | Logging, Monitoring & Audit | Lack of compartment-wide aggregated log sinks | Prevents centralized security monitoring, efficient auditing, and comprehensive visibility across the compartment. | Set up an aggregated log sink to a central Log Analytics group for comprehensive logging and analysis. | Platform Operations | High`

---

INVENTORY SNAPSHOT PROVIDED BELOW:
: {data_text}